<?php $__env->startSection('title', 'Détail du Recouvrement'); ?>

<?php $__env->startPush('styles'); ?>
<style>
    /* Style général de la page */
    .recovery-detail {
        background-color: #f8f9fa;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
        padding: 20px;
        margin-bottom: 30px;
    }
    
    /* En-tête avec informations principales */
    .recovery-header {
        background: linear-gradient(to right bottom, #1E88E5, #0D47A1);
        color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }
    
    .recovery-header h2 {
        margin-bottom: 15px;
        font-weight: 600;
    }
    
    .recovery-header .badge {
        font-size: 0.8rem;
        padding: 5px 10px;
        border-radius: 30px;
        margin-left: 10px;
    }
    
    /* Cartes d'information */
    .info-card {
        background-color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        height: 100%;
        transition: all 0.3s ease;
    }
    
    .info-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
    }
    
    .info-card h5 {
        color: #1E88E5;
        font-weight: 600;
        margin-bottom: 15px;
        display: flex;
        align-items: center;
    }
    
    .info-card h5 i {
        margin-right: 10px;
    }
    
    .info-card .detail-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 10px;
        padding-bottom: 10px;
        border-bottom: 1px solid #f0f0f0;
    }
    
    .info-card .detail-item:last-child {
        border-bottom: none;
        padding-bottom: 0;
        margin-bottom: 0;
    }
    
    .info-card .detail-label {
        color: #6c757d;
        font-weight: 500;
    }
    
    .info-card .detail-value {
        font-weight: 600;
        text-align: right;
    }
    
    /* Barre de progression */
    .payment-progress {
        height: 10px;
        border-radius: 5px;
        margin: 15px 0;
    }
    
    /* Badges et étiquettes */
    .badge-paid {
        background-color: rgba(25, 135, 84, 0.1);
        color: #198754;
    }
    
    .badge-partial {
        background-color: rgba(255, 193, 7, 0.1);
        color: #ffc107;
    }
    
    .badge-unpaid {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
    }
    
    /* Boutons d'action */
    .action-buttons .btn {
        padding: 10px 20px;
        border-radius: 5px;
        font-weight: 500;
        margin-right: 10px;
        display: inline-flex;
        align-items: center;
    }
    
    .action-buttons .btn i {
        margin-right: 8px;
    }
    
    /* Tableau des paiements */
    .payments-table {
        background-color: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    }
    
    .payments-table thead {
        background-color: #1E88E5;
        color: white;
    }
    
    .payments-table th {
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.5px;
        padding: 12px 15px;
    }
    
    .payments-table td {
        padding: 12px 15px;
        vertical-align: middle;
    }
    
    /* Responsive */
    @media (max-width: 768px) {
        .recovery-header {
            padding: 15px;
        }
        
        .info-card {
            margin-bottom: 15px;
        }
        
        .action-buttons .btn {
            margin-bottom: 10px;
            width: 100%;
        }
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid mt-4">
    <!-- Bouton de retour -->
    <div class="mb-3">
        <a href="<?php echo e(route('accountant.recoveries.index')); ?>" class="btn btn-outline-primary">
            <i class="fas fa-arrow-left me-2"></i> Retour à la liste
        </a>
    </div>
    
    <!-- En-tête avec informations principales -->
    <div class="recovery-header">
        <div class="d-flex justify-content-between align-items-center flex-wrap">
            <div>
                <h2>
                    Facture #<?php echo e($sale->invoice_number); ?>

                    <?php if($paymentStatus == 'paid'): ?>
                        <span class="badge bg-success">Payé</span>
                    <?php elseif($paymentStatus == 'partial'): ?>
                        <span class="badge bg-warning">Paiement Partiel</span>
                    <?php else: ?>
                        <span class="badge bg-danger">Impayé</span>
                    <?php endif; ?>
                </h2>
                <p class="mb-0">Date de création: <?php echo e($sale->created_at->format('d/m/Y à H:i')); ?></p>
            </div>
            <div class="action-buttons">
                <?php if($sale->admin_validation_status !== 'rejected'): ?>
                <a href="<?php echo e(route('accountant.recoveries.edit', $sale->id)); ?>" class="btn btn-success">
                    <i class="fas fa-money-bill-wave"></i> Enregistrer un paiement
                </a>
                <?php endif; ?>
                <a href="tel:<?php echo e($sale->customer_phone); ?>" class="btn btn-info text-white">
                    <i class="fas fa-phone"></i> Contacter le client
                </a>
                <?php if($sale->admin_validation_status === 'rejected'): ?>
                <div class="alert alert-danger mt-2">
                    <i class="fas fa-ban"></i> Cette vente a été rejetée par l'administrateur et ne peut pas recevoir de paiement.
                    <?php if($sale->admin_note): ?>
                    <p class="mb-0 mt-1"><strong>Raison :</strong> <?php echo e($sale->admin_note); ?></p>
                    <?php endif; ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <div class="row">
        <!-- Informations client -->
        <div class="col-md-4 mb-4">
            <div class="info-card">
                <h5><i class="fas fa-user"></i> Informations Client</h5>
                <div class="detail-item">
                    <span class="detail-label">Nom</span>
                    <span class="detail-value"><?php echo e($sale->customer_name); ?></span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Téléphone</span>
                    <span class="detail-value"><?php echo e($sale->customer_phone); ?></span>
                </div>
                <?php if($sale->customer_email): ?>
                <div class="detail-item">
                    <span class="detail-label">Email</span>
                    <span class="detail-value"><?php echo e($sale->customer_email); ?></span>
                </div>
                <?php endif; ?>
                <?php if($sale->customer_address): ?>
                <div class="detail-item">
                    <span class="detail-label">Adresse</span>
                    <span class="detail-value"><?php echo e($sale->customer_address); ?></span>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Détails de la facture -->
        <div class="col-md-4 mb-4">
            <div class="info-card">
                <h5><i class="fas fa-file-invoice"></i> Détails de la Facture</h5>
                <div class="detail-item">
                    <span class="detail-label">Numéro</span>
                    <span class="detail-value"><?php echo e($sale->invoice_number); ?></span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Date</span>
                    <span class="detail-value"><?php echo e($sale->created_at->format('d/m/Y')); ?></span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Âge</span>
                    <span class="detail-value"><?php echo e($sale->created_at->diffInDays(now())); ?> jours</span>
                </div>
                <?php if($sale->due_date): ?>
                <div class="detail-item">
                    <span class="detail-label">Échéance</span>
                    <span class="detail-value"><?php echo e($sale->due_date->format('d/m/Y')); ?></span>
                </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- Statut de paiement -->
        <div class="col-md-4 mb-4">
            <div class="info-card">
                <h5><i class="fas fa-chart-pie"></i> Statut de Paiement</h5>
                <div class="detail-item">
                    <span class="detail-label">Montant total</span>
                    <span class="detail-value"><?php echo e(number_format($totalAmount, 0, ',', ' ')); ?> FCFA</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Montant payé</span>
                    <span class="detail-value text-success"><?php echo e(number_format($paidAmount, 0, ',', ' ')); ?> FCFA</span>
                </div>
                <div class="detail-item">
                    <span class="detail-label">Reste à payer</span>
                    <span class="detail-value <?php echo e($remainingAmount > 0 ? 'text-danger' : 'text-success'); ?>"><?php echo e(number_format($remainingAmount, 0, ',', ' ')); ?> FCFA</span>
                </div>
                
                <!-- Barre de progression -->
                <div class="mt-3">
                    <div class="d-flex justify-content-between mb-1">
                        <span>Progression du paiement</span>
                        <span><?php echo e(round($paymentPercentage)); ?>%</span>
                    </div>
                    <div class="progress payment-progress">
                        <div class="progress-bar <?php echo e($paymentPercentage == 100 ? 'bg-success' : ($paymentPercentage > 0 ? 'bg-warning' : 'bg-danger')); ?>" 
                            role="progressbar" 
                            style="width: <?php echo e($paymentPercentage); ?>%" 
                            aria-valuenow="<?php echo e($paymentPercentage); ?>" 
                            aria-valuemin="0" 
                            aria-valuemax="100">
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Historique des paiements -->
    <div class="row">
        <div class="col-12">
            <div class="info-card">
                <h5><i class="fas fa-history"></i> Historique des Paiements</h5>
                
                <?php if($sale->payments && $sale->payments->count() > 0): ?>
                <div class="table-responsive payments-table">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Réf. Paiement</th>
                                <th>Montant</th>
                                <th>Méthode</th>
                                <th>Réf. Transaction</th>
                                <th>Agent</th>
                                <th>Poste</th>
                                <th>Statut</th>
                                <th>Commentaire</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $sale->payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><?php echo e($payment->created_at->format('d/m/Y H:i')); ?></td>
                                <td><span class="badge bg-primary"><?php echo e($payment->reference ?? 'N/A'); ?></span></td>
                                <td><?php echo e(number_format($payment->amount, 0, ',', ' ')); ?> FCFA</td>
                                <td><?php echo e($payment->payment_method); ?></td>
                                <td><?php echo e($payment->reference_number ?? '-'); ?></td>
                                <td><?php echo e($payment->cashier->name ?? 'N/A'); ?></td>
                                <td><?php echo e($payment->position ?? 'N/A'); ?></td>
                                <td>
                                    <?php if($payment->status == 'completed'): ?>
                                        <span class="badge badge-paid">Complété</span>
                                    <?php elseif($payment->status == 'pending'): ?>
                                        <span class="badge badge-partial">En attente</span>
                                    <?php else: ?>
                                        <span class="badge badge-unpaid">Échoué</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo e($payment->notes ?? '-'); ?></td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
                <?php else: ?>
                <div class="text-center py-4">
                    <i class="fas fa-exclamation-circle fa-3x text-muted mb-3"></i>
                    <p class="mb-0">Aucun paiement enregistré pour cette vente.</p>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    
    <!-- Produits de la vente -->
    <?php if(isset($sale->items) && count($sale->items) > 0): ?>
    <div class="row mt-4">
        <div class="col-12">
            <div class="info-card">
                <h5><i class="fas fa-shopping-cart"></i> Produits</h5>
                
                <div class="table-responsive payments-table">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Produit</th>
                                <th>Quantité</th>
                                <th>Prix unitaire</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__currentLoopData = $sale->items; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><?php echo e($item->product_name); ?></td>
                                <td><?php echo e($item->quantity); ?></td>
                                <td><?php echo e(number_format($item->unit_price, 0, ',', ' ')); ?> FCFA</td>
                                <td><?php echo e(number_format($item->quantity * $item->unit_price, 0, ',', ' ')); ?> FCFA</td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
    // Initialiser les tooltips Bootstrap
    document.addEventListener('DOMContentLoaded', function() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.accountant', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/accountant/recoveries/show.blade.php ENDPATH**/ ?>
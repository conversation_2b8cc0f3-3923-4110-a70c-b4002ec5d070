<?php $__env->startSection('content'); ?>
<div class="container-fluid py-4">
    <div class="card shadow-sm">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4 class="card-title mb-0">
                    <i class="fas fa-truck-loading text-primary me-2"></i>
                    Détails de l'approvisionnement #<?php echo e($supply->reference); ?>

                </h4>
                <a href="<?php echo e(route('cement-manager.dashboard')); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Retour
                </a>
            </div>

            <!-- Ajout des statistiques -->
            <div class="row mb-4">
                <?php
                    $totalProducts = $supply->details->count() ?? 0;
                    $totalCities = $supply->cities->count() ?? 0;
                    $totalTrips = $supply->cities->sum('trips') ?? 0;
                ?>
                <div class="col-md-3">
                    <div class="card border-0 bg-primary bg-gradient h-100 stat-card">
                        <div class="card-body text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-subtitle mb-2">Total Produits</h6>
                                    <h2 class="card-title mb-0 display-6"><?php echo e($totalProducts); ?></h2>
                                </div>
                                <i class="fas fa-boxes fa-2x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 bg-success bg-gradient h-100 stat-card">
                        <div class="card-body text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-subtitle mb-2">Villes Livrées</h6>
                                    <h2 class="card-title mb-0 display-6"><?php echo e($totalCities); ?></h2>
                                </div>
                                <i class="fas fa-city fa-2x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 bg-info bg-gradient h-100 stat-card">
                        <div class="card-body text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-subtitle mb-2">Total Voyages</h6>
                                    <h2 class="card-title mb-0 display-6"><?php echo e($totalTrips); ?></h2>
                                </div>
                                <i class="fas fa-exchange-alt fa-2x opacity-50"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card border-0 bg-warning bg-gradient h-100 stat-card">
                        <div class="card-body text-white">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="card-subtitle mb-2">Total Ventes</h6>
                                    <h2 class="card-title mb-0"><?php echo e(number_format($totalAmount ?? 0, 0, ',', ' ')); ?></h2>
                                </div>
                                <i class="fas fa-money-bill-wave fa-2x opacity-50"></i>
                            </div>
                            <small>FCFA</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card border-0 bg-light">
                        <div class="card-body">
                            <h5 class="card-title mb-3">
                                <i class="fas fa-info-circle text-primary me-2"></i>
                                Informations générales
                            </h5>
                            <table class="table table-sm">
                                <tr>
                                    <th class="text-muted">Référence :</th>
                                    <td><?php echo e($supply->reference); ?></td>
                                </tr>
                                <tr>
                                    <th class="text-muted">Date :</th>
                                    <td><?php echo e($supply->created_at->format('d/m/Y')); ?></td>
                                </tr>
                                <tr>
                                    <th class="text-muted">Fournisseur :</th>
                                    <td><?php echo e($supply->supplier->name); ?></td>
                                </tr>
                                <?php if($supply->validator): ?>
                                    <tr>
                                        <th class="text-muted">Validé par :</th>
                                        <td><?php echo e($supply->validator->name); ?></td>
                                    </tr>
                                    <tr>
                                        <th class="text-muted">Date de validation :</th>
                                        <td><?php echo e($supply->validated_at->format('d/m/Y H:i')); ?></td>
                                    </tr>
                                <?php endif; ?>
                            </table>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card border-0 bg-light h-100">
                        <div class="card-body">
                            <h5 class="card-title mb-3">
                                <i class="fas fa-sticky-note text-primary me-2"></i>
                                Notes
                            </h5>
                            <p class="card-text text-muted mb-0"><?php echo e($supply->notes ?? 'Aucune note'); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card border-0 bg-light mb-4">
                <div class="card-body">
                    <h5 class="card-title mb-3">
                        <i class="fas fa-map-marker-alt text-primary me-2"></i>
                        Détails des livraisons par ville
                    </h5>
                    <div class="table-responsive">
                        <table class="table table-hover table-striped">
                            <thead class="bg-primary text-white">
                                <tr>
                                    <th>Ville</th>
                                    <th>Produit</th>
                                    <th>Véhicule</th>
                                    <th>Chauffeur</th>
                                    <th>Voyages</th>
                                    <th>Quantité</th>
                                    <th>Prix de vente</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                    $totalAmount = 0;
                                ?>
                                <?php $__currentLoopData = $supply->cities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $city): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                        $details = $supply->details;
                                        $total = 0;
                                        $quantity = $city->quantity ?? 0;
                                        $price = 0;
                                        
                                        if ($city->city_id && $details->isNotEmpty()) {
                                            foreach ($details as $detail) {
                                                if ($detail->product) {
                                                    $price = $detail->product->getPriceForCity($city->city_id);
                                                    $total += $quantity * $price;
                                                }
                                            }
                                        }
                                        $totalAmount += $total;
                                    ?>
                                    <tr>
                                        <td>
                                            <span class="badge bg-info text-white">
                                                <i class="fas fa-city me-1"></i>
                                                <?php echo e($city->city->name ?? 'N/A'); ?>

                                            </span>
                                        </td>
                                        <td>
                                            <?php $__currentLoopData = $details; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $detail): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php if($detail->product): ?>
                                                    <span class="badge bg-secondary me-1">
                                                        <?php echo e($detail->product->name); ?>

                                                    </span>
                                                <?php endif; ?>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </td>
                                        <td>
                                            <?php if($city->vehicle): ?>
                                                <span class="badge bg-success">
                                                    <i class="fas fa-truck me-1"></i>
                                                    <?php echo e($city->vehicle->registration_number); ?>

                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-warning text-dark">
                                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                                    Non assigné
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if($city->driver): ?>
                                                <span class="badge bg-success">
                                                    <i class="fas fa-user me-1"></i>
                                                    <?php echo e($city->driver->first_name); ?> <?php echo e($city->driver->last_name); ?>

                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-warning text-dark">
                                                    <i class="fas fa-exclamation-triangle me-1"></i>
                                                    Non assigné
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td class="text-center"><?php echo e($city->trips); ?></td>
                                        <td class="text-end"><?php echo e(number_format($quantity, 2, ',', ' ')); ?> T</td>
                                        <td class="text-end"><?php echo e(number_format($price, 0, ',', ' ')); ?> FCFA</td>
                                        <td class="text-end"><?php echo e(number_format($total, 0, ',', ' ')); ?> FCFA</td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </tbody>
                            <tfoot>
                                <tr class="bg-primary text-white">
                                    <td colspan="7" class="text-end">
                                        <strong>Total des ventes</strong>
                                    </td>
                                    <td class="text-end">
                                        <strong><?php echo e(number_format($totalAmount, 0, ',', ' ')); ?> FCFA</strong>
                                    </td>
                                </tr>
                            </tfoot>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php $__env->startPush('styles'); ?>
<style>
    .table th {
        font-weight: 600;
    }
    .badge {
        font-size: 0.9em;
        padding: 0.5em 0.7em;
        transition: all 0.3s ease;
    }
    .badge:hover {
        transform: translateY(-2px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .table-hover tbody tr {
        transition: all 0.2s ease;
    }
    .table-hover tbody tr:hover {
        background-color: rgba(0,0,0,.03);
        transform: scale(1.002);
    }
    .card {
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        overflow: hidden;
    }
    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .card-title {
        font-weight: 600;
    }
    .bg-light {
        background-color: #f8f9fa !important;
    }
    .btn {
        transition: all 0.3s ease;
    }
    .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .table td, .table th {
        vertical-align: middle;
    }
    .bg-primary {
        background: linear-gradient(45deg, #007bff, #0056b3) !important;
    }
    .text-primary {
        background: linear-gradient(45deg, #007bff, #0056b3);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
    .badge.bg-info {
        background: linear-gradient(45deg, #17a2b8, #138496) !important;
    }
    .badge.bg-success {
        background: linear-gradient(45deg, #28a745, #1e7e34) !important;
    }
    .badge.bg-warning {
        background: linear-gradient(45deg, #ffc107, #d39e00) !important;
    }
    .badge.bg-secondary {
        background: linear-gradient(45deg, #6c757d, #545b62) !important;
    }
    .badge.bg-light {
        background: linear-gradient(45deg, #f8f9fa, #e9ecef) !important;
    }
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    @keyframes slideIn {
        from { transform: translateX(-20px); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes scaleIn {
        from { transform: scale(0.9); opacity: 0; }
        to { transform: scale(1); opacity: 1; }
    }
    .card {
        animation: fadeIn 0.5s ease-out;
    }
    .table-responsive {
        animation: fadeIn 0.7s ease-out;
    }
    .stat-card {
        animation: scaleIn 0.5s ease-out forwards;
        opacity: 0;
    }
    .stat-card:nth-child(1) { animation-delay: 0.1s; }
    .stat-card:nth-child(2) { animation-delay: 0.2s; }
    .stat-card:nth-child(3) { animation-delay: 0.3s; }
    .stat-card:nth-child(4) { animation-delay: 0.4s; }
    
    .stat-card .fa-2x {
        transition: all 0.3s ease;
    }
    .stat-card:hover .fa-2x {
        transform: scale(1.2) rotate(10deg);
        opacity: 0.7;
    }
    .stat-card .display-6 {
        font-size: 2rem;
        font-weight: 600;
        margin: 0;
    }
    .stat-card .card-subtitle {
        text-transform: uppercase;
        font-size: 0.8rem;
        letter-spacing: 1px;
        opacity: 0.8;
    }
    .stat-card small {
        opacity: 0.8;
        font-size: 0.75rem;
    }
    .bg-gradient {
        position: relative;
        overflow: hidden;
    }
    .bg-gradient::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0));
        transition: all 0.3s ease;
    }
    .bg-gradient:hover::before {
        transform: scale(1.2) rotate(10deg);
    }
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animation des lignes du tableau au défilement
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, { threshold: 0.1 });

    document.querySelectorAll('tbody tr').forEach(tr => {
        tr.style.opacity = '0';
        tr.style.transform = 'translateY(20px)';
        tr.style.transition = 'all 0.5s ease';
        observer.observe(tr);
    });

    // Animation des badges
    document.querySelectorAll('.badge').forEach(badge => {
        badge.addEventListener('mouseover', function() {
            this.style.transform = 'translateY(-2px) scale(1.1)';
        });
        badge.addEventListener('mouseout', function() {
            this.style.transform = 'translateY(0) scale(1)';
        });
    });

    // Animation des totaux
    const totals = document.querySelectorAll('.table-light, .bg-primary');
    totals.forEach(total => {
        total.style.transition = 'all 0.3s ease';
        total.addEventListener('mouseover', function() {
            this.style.transform = 'scale(1.005)';
        });
        total.addEventListener('mouseout', function() {
            this.style.transform = 'scale(1)';
        });
    });

    // Animation des cartes de statistiques
    document.querySelectorAll('.stat-card').forEach(card => {
        card.addEventListener('mouseover', function() {
            this.querySelector('.fa-2x').style.transform = 'scale(1.2) rotate(10deg)';
        });
        card.addEventListener('mouseout', function() {
            this.querySelector('.fa-2x').style.transform = 'scale(1) rotate(0)';
        });
    });

    // Mise en surbrillance des lignes liées
    document.querySelectorAll('tbody tr').forEach(tr => {
        tr.addEventListener('mouseover', function() {
            const cityName = this.querySelector('td:first-child')?.textContent.trim();
            if (cityName) {
                document.querySelectorAll('tbody tr').forEach(row => {
                    if (row.querySelector('td:first-child')?.textContent.trim() === cityName) {
                        row.style.backgroundColor = 'rgba(0,123,255,0.05)';
                    }
                });
            }
        });
        
        tr.addEventListener('mouseout', function() {
            document.querySelectorAll('tbody tr').forEach(row => {
                row.style.backgroundColor = '';
            });
        });
    });

    // Animation des icônes dans les cellules du tableau
    document.querySelectorAll('.table i').forEach(icon => {
        icon.style.transition = 'all 0.3s ease';
        icon.addEventListener('mouseover', function() {
            this.style.transform = 'scale(1.2)';
            this.style.color = '#007bff';
        });
        icon.addEventListener('mouseout', function() {
            this.style.transform = 'scale(1)';
            this.style.color = '';
        });
    });

    // Effet de pulsation sur les totaux
    function pulseTotal() {
        const totalRow = document.querySelector('.bg-primary.text-white');
        if (totalRow) {
            totalRow.style.transition = 'all 0.3s ease';
            totalRow.style.transform = 'scale(1.002)';
            setTimeout(() => {
                totalRow.style.transform = 'scale(1)';
            }, 200);
        }
    }

    // Pulsation toutes les 5 secondes
    setInterval(pulseTotal, 5000);
});
</script>
<?php $__env->stopPush(); ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/cement-manager/supplies/show.blade.php ENDPATH**/ ?>
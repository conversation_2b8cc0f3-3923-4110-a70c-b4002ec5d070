<?php $__env->startSection('title', 'Tableau de bord - Gestionnaire Ciment'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Tableau de bord</h1>
    </div>

    <!-- Statistiques -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <h5 class="card-title">Total Bons de Commande</h5>
                    <h2 class="card-text"><?php echo e($stats['total_supplies']); ?></h2>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <h5 class="card-title">Montant Total</h5>
                    <h2 class="card-text"><?php echo e(number_format($stats['total_amount'], 2, ',', ' ')); ?> FCFA</h2>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <h5 class="card-title">Total Articles</h5>
                    <h2 class="card-text"><?php echo e($stats['total_products']); ?></h2>
                </div>
            </div>
        </div>
    </div>

    <!-- Liste des approvisionnements -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">Approvisionnements Validés</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead>
                        <tr>
                            <th>Référence</th>
                            <th>Date</th>
                            <th>Fournisseur</th>
                            <th>Articles</th>
                            <th>Montant Total</th>
                            <th>Validé par</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $supplies; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $supply): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td><?php echo e($supply->reference); ?></td>
                                <td><?php echo e($supply->created_at->format('d/m/Y')); ?></td>
                                <td><?php echo e($supply->supplier->name); ?></td>
                                <td>
                                    <ul class="list-unstyled mb-0">
                                        <?php $__currentLoopData = $supply->details; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <li>
                                                <?php if($item->product->category->name === 'Ciment'): ?>
                                                    <?php echo e(number_format($item->tonnage, 2, ',', ' ')); ?> T
                                                <?php else: ?>
                                                    <?php echo e($item->quantity); ?>

                                                <?php endif; ?>
                                                <?php echo e($item->product->name); ?>

                                            </li>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </ul>
                                </td>
                                <td><?php echo e(number_format($supply->total_sale_amount, 2, ',', ' ')); ?> FCFA</td>
                                <td>
                                    <?php if($supply->validator): ?>
                                        <?php echo e($supply->validator->first_name); ?> <?php echo e($supply->validator->last_name); ?>

                                    <?php else: ?>
                                        <span class="text-muted">Non spécifié</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="<?php echo e(route('cement-manager.supplies.show', $supply->id)); ?>" class="btn btn-info btn-sm">
                                        <i class="fas fa-eye"></i> Détails
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="7" class="text-center">Aucun approvisionnement validé trouvé</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.cement_manager', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/cement-manager/dashboard.blade.php ENDPATH**/ ?>
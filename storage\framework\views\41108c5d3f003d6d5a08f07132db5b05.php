<?php $__env->startSection('title', 'Reçu de paiement #' . $payment->id); ?>

<?php $__env->startSection('styles'); ?>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
<style>
    /* Variables */
    :root {
        --primary-color: #1E88E5;
        --primary-light: #BBDEFB;
        --primary-dark: #0D47A1;
        --secondary-color: #4CAF50;
        --secondary-light: #E8F5E9;
        --warning-color: #FF9800;
        --warning-light: #FFF3E0;
        --danger-color: #F44336;
        --danger-light: #FFEBEE;
        --info-color: #00BCD4;
        --info-light: #E0F7FA;
        --dark-color: #101828;
        --text-color: #344054;
        --text-light: #667085;
        --border-color: #EAECF0;
        --background-color: #F9FAFB;
        --gradient-blue: linear-gradient(135deg, #1E88E5, #0D47A1);
        --box-shadow: 0 8px 20px rgba(13, 71, 161, 0.08);
    }
    
    /* Styles généraux pour le format A5 */
    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.5;
        color: var(--text-color);
        background-color: #f5f5f5;
        margin: 0;
        padding: 0;
    }
    
    /* Format A5 : 148mm x 210mm */
    .receipt-page {
        width: 148mm;
        min-height: 210mm;
        margin: 0 auto 2rem;
        background: white;
        position: relative;
        padding: 0;
        box-shadow: var(--box-shadow);
        overflow: hidden;
        border-radius: 8px;
    }
    
    .receipt-container {
        width: 100%;
        height: 100%;
        background-color: #fff;
        position: relative;
        overflow: hidden;
        max-width: 148mm;
        margin: 0 auto;
    }
    
    /* En-tête du reçu avec dégradé - hauteur fortement réduite */
    .receipt-header {
        background: var(--gradient-blue);
        color: white;
        padding: 0.3rem 0.5rem;
        text-align: center;
        position: relative;
        overflow: hidden;
    }
    
    /* Logo container avec effet de brillance */
    .receipt-logo-container {
        background-color: white;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 0.5rem;
        padding: 5px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
        z-index: 10;
    }
    
    /* Effet de brillance sur le logo supprimé pour optimiser l'espace */
    
    .receipt-logo {
        max-width: 40px;
        max-height: 40px;
        z-index: 2;
    }
    
    /* Titre du reçu avec effet de texte */
    .receipt-title {
        font-size: 1.2rem;
        font-weight: 800;
        margin-bottom: 0.2rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        color: white;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    }
    
    /* Badge pour le numéro de reçu */
    .receipt-number {
        font-size: 0.9rem;
        color: rgba(255, 255, 255, 0.9);
        margin-bottom: 0.2rem;
        font-weight: 600;
    }
    
    /* Date du reçu avec icône - format compact */
    .receipt-date {
        font-size: 0.8rem;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 0.3rem;
        opacity: 0.9;
        margin-bottom: 0;
    }
    
    .receipt-date i {
        font-size: 0.7rem;
    }
    
    /* Corps du reçu avec design moderne - espacement minimal */
    .receipt-body {
        padding: 0.75rem 0.75rem;
        position: relative;
        background-color: #FAFBFF;
        background-image: none;
    }
    
    /* Conteneur pour les informations client et vente */
    .receipt-info-container {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        gap: 0.3rem;
        margin-bottom: 0.3rem;
    }
    
    .receipt-info-column {
        width: 49%;
    }
    
    /* Sections avec style de carte moderne - espacement minimal */
    .receipt-section {
        margin-bottom: 0.5rem;
        background-color: white;
        border-radius: 6px;
        padding: 0.5rem;
        box-shadow: 0 2px 6px rgba(13, 71, 161, 0.03);
        border: 1px solid rgba(187, 222, 251, 0.2);
        overflow: hidden;
        position: relative;
    }
    
    /* Désactivation de l'effet hover pour l'impression */
    @media screen {
        .receipt-section:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(13, 71, 161, 0.08);
        }
    }
    
    /* Titre de section avec accent color - espacement réduit */
    .receipt-section-title {
        font-size: 0.9rem;
        color: var(--primary-dark);
        margin-bottom: 0.5rem;
        display: flex;
        align-items: center;
        padding-bottom: 0.5rem;
        font-weight: 600;
        border-bottom: 1px dashed var(--border-color);
    }
    
    .receipt-section-title i {
        margin-right: 0.4rem;
        color: var(--primary-color);
        font-size: 0.9rem;
    }
    
    .receipt-section-title::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 50px;
        height: 3px;
        background: var(--gradient-blue);
        border-radius: 10px;
    }
    
    /* Icônes de section avec style moderne */
    .receipt-section-title i {
        margin-right: 0.75rem;
        background: var(--primary-light);
        color: var(--primary-dark);
        width: 28px;
        height: 28px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.9rem;
    }
    
    /* Lignes d'information avec style moderne - format compact */
    .info-row {
        display: flex;
        margin-bottom: 0.3rem;
        align-items: flex-start;
    }
    
    .info-row:last-child {
        margin-bottom: 0;
    }
    
    .info-label {
        font-weight: 600;
        color: #0D47A1;
        min-width: 100px;
        font-size: 0.8rem;
    }
    
    .info-value {
        flex: 1;
        font-size: 0.8rem;
        color: #333;
        min-width: 60px;
        display: inline-block;
    }
    
    /* Tableau des produits avec design moderne - espacement réduit */
    .receipt-table {
        width: 100%;
        border-collapse: separate;
        border-spacing: 0;
        margin-bottom: 0.75rem;
        font-size: 0.75rem;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(13, 71, 161, 0.05);
    }
    
    /* En-tête du tableau avec dégradé */
    .receipt-table th {
        background: var(--gradient-blue);
        color: white;
        padding: 0.5rem 0.4rem;
        text-align: left;
        font-weight: 600;
        position: relative;
        text-transform: uppercase;
        font-size: 0.7rem;
        letter-spacing: 0.3px;
    }
    
    .receipt-table th:first-child {
        border-top-left-radius: 8px;
        padding-left: 0.75rem;
    }
    
    .receipt-table th:last-child {
        border-top-right-radius: 8px;
        padding-right: 0.75rem;
    }
    
    /* Cellules du tableau avec espacement réduit */
    .receipt-table td {
        padding: 0.5rem 0.4rem;
        border-bottom: 1px solid var(--border-color);
        background-color: white;
    }
    
    .receipt-table td:first-child {
        padding-left: 0.75rem;
    }
    
    .receipt-table td:last-child {
        padding-right: 0.75rem;
        font-weight: 700;
    }
    
    /* Effet de survol uniquement sur écran */
    @media screen {
        .receipt-table tr:hover td {
            background-color: rgba(187, 222, 251, 0.1);
        }
    }
    
    .receipt-table tr:last-child td {
        border-bottom: none;
    }
    
    .receipt-table tr:last-child td:first-child {
        border-bottom-left-radius: 8px;
    }
    
    .receipt-table tr:last-child td:last-child {
        border-bottom-right-radius: 8px;
    }
    
    /* Résumé des paiements avec style moderne */
    .payment-summary {
        background: white;
        color: var(--text-color);
        padding: 0;
        border-radius: 12px;
        font-size: 0.9rem;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(13, 71, 161, 0.06);
        border: 1px solid rgba(187, 222, 251, 0.3);
        position: relative;
    }
    
    /* Barre d'accent en haut du résumé */
    .payment-summary::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: var(--gradient-blue);
    }
    
    /* Titre du résumé des paiements */
    .payment-summary-title {
        padding: 1rem 1.25rem;
        font-weight: 600;
        color: var(--primary-dark);
        border-bottom: 1px solid var(--border-color);
        display: flex;
        align-items: center;
        background-color: rgba(187, 222, 251, 0.1);
    }
    
    .payment-summary-title i {
        margin-right: 0.75rem;
        background: var(--primary-light);
        color: var(--primary-dark);
        width: 28px;
        height: 28px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    /* Contenu du résumé des paiements */
    .payment-summary-content {
        padding: 1.25rem;
    }
    
    /* Lignes du résumé des paiements */
    .payment-summary-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.75rem;
        padding-bottom: 0.75rem;
        border-bottom: 1px dashed rgba(187, 222, 251, 0.5);
        font-weight: 500;
    }
    
    .payment-summary-row:last-child {
        margin-bottom: 0;
        padding-bottom: 0;
        padding-top: 0.75rem;
        border-bottom: none;
        border-top: 1px solid rgba(13, 71, 161, 0.1);
        font-weight: 700;
        font-size: 1.1rem;
    }
    
    /* Valeurs dans le résumé des paiements */
    .payment-summary-value {
        font-weight: 600;
    }
    
    .payment-summary-row:last-child .payment-summary-value {
        color: var(--primary-dark);
    }
    
    /* Badge pour le mode de paiement avec style moderne */
    .payment-method-badge {
        display: inline-flex;
        align-items: center;
        background: var(--gradient-blue);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 30px;
        font-size: 0.9rem;
        font-weight: 600;
        box-shadow: 0 4px 10px rgba(13, 71, 161, 0.2);
        transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    
    .payment-method-badge:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(13, 71, 161, 0.3);
    }
    
    .payment-method-badge i {
        margin-right: 0.5rem;
        font-size: 1rem;
    }
    
    /* Badges de statut avec couleurs distinctes */
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 0.4rem 0.75rem;
        border-radius: 30px;
        font-size: 0.8rem;
        font-weight: 600;
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
    }
    
    .status-badge:hover {
        transform: translateY(-2px);
    }
    
    .status-badge.pending {
        background: linear-gradient(to right, #FF9800, #F57C00);
        color: white;
    }
    
    .status-badge.partial {
        background: linear-gradient(to right, #03A9F4, #0288D1);
        color: white;
    }
    
    .status-badge.completed {
        background: linear-gradient(to right, #4CAF50, #2E7D32);
        color: white;
    }
    
    .status-badge i {
        margin-right: 0.4rem;
        font-size: 0.8rem;
    }
    
    /* Section des signatures avec style moderne - format compact */
    .receipt-signatures {
        display: flex;
        justify-content: space-between;
        margin-top: 0.4rem;
        margin-bottom: 0.4rem;
    }
    
    .signature-box {
        text-align: center;
        width: 30%;
        position: relative;
    }
    
    .signature-box::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: var(--gradient-blue);
    }
    
    .signature-line {
        border-bottom: 1px dashed var(--primary-light);
        margin-bottom: 0.2rem;
        height: 10px;
        position: relative;
    }
    
    .signature-line::before {
        content: '✒';
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        font-size: 0.9rem;
        opacity: 0.2;
    }
    
    .signature-title {
        font-size: 0.7rem;
        color: var(--primary-dark);
        font-weight: 600;
        margin-top: 0;
        margin-bottom: 0;
    }
    
    /* Styles du pied de page supprimés */
    
    /* Code QR avec style minimal */
    .receipt-qr {
        position: absolute;
        right: 0.75rem;
        bottom: 0.75rem;
        text-align: center;
        opacity: 1;
    }
    
    @media screen {
        .receipt-qr:hover {
            transform: scale(1.05);
            opacity: 1;
        }
    }
    
    .receipt-qr img {
        max-width: 40px;
        border-radius: 3px;
        padding: 1px;
        background-color: white;
        box-shadow: 0 1px 2px rgba(13, 71, 161, 0.1);
        border: 1px solid rgba(187, 222, 251, 0.3);
    }
    
    .receipt-qr-text {
        font-size: 0.5rem;
        color: var(--primary-dark);
        margin-top: 0.1rem;
        font-weight: 600;
    }
    
    /* Section des termes et conditions - format minimal */
    .terms-conditions {
        font-size: 0.55rem;
        color: var(--text-light);
        text-align: center;
        margin-top: 0.3rem;
        padding: 0.2rem;
        border-top: 1px dashed rgba(187, 222, 251, 0.3);
        line-height: 1.1;
    }
    
    .terms-conditions-title {
        font-weight: 600;
        margin-bottom: 0.1rem;
        color: var(--primary-dark);
        font-size: 0.6rem;
    }
    
    /* Styles pour le cachet d'entreprise - taille réduite */
    .company-stamp {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 25px;
        margin-bottom: 0.5rem;
    }
    
    .stamp-circle {
        width: 50px;
        height: 50px;
        border: 1px dashed var(--primary-dark);
        border-radius: 50%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        transform: rotate(-15deg);
        position: relative;
        background-color: rgba(13, 71, 161, 0.05);
    }
    
    .stamp-circle::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 42px;
        height: 42px;
        border: 1px solid var(--primary-light);
        border-radius: 50%;
        opacity: 0.5;
    }
    
    .stamp-text {
        font-weight: 800;
        color: var(--primary-dark);
        font-size: 0.8rem;
        letter-spacing: 0.5px;
    }
    
    .stamp-subtext {
        font-size: 0.5rem;
        color: var(--primary-dark);
        font-weight: 600;
        letter-spacing: 0.5px;
        margin-top: 1px;
    }
    
    .receipt-qr-text {
        font-size: 0.7rem;
        color: var(--text-light);
        margin-top: 0.3rem;
    }
    
    .receipt-note {
        background-color: #FFF8E1;
        border-left: 4px solid var(--accent-color);
        padding: 1rem;
        border-radius: 5px;
        font-size: 0.9rem;
        color: #795548;
        margin-top: 2rem;
        display: flex;
        align-items: center;
    }
    
    .receipt-note i {
        font-size: 1.5rem;
        margin-right: 1rem;
        color: var(--accent-color);
    }
    
    .receipt-actions {
        margin-top: 2rem;
        display: flex;
        justify-content: center;
        gap: 1rem;
    }
    
    .btn-print {
        background-color: var(--primary-color);
        color: white;
        border: none;
        padding: 0.8rem 2rem;
        border-radius: 50px;
        font-weight: 600;
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
        box-shadow: 0 4px 10px rgba(30, 136, 229, 0.3);
    }
    
    .btn-print:hover {
        background-color: var(--primary-dark);
        transform: translateY(-2px);
        box-shadow: 0 6px 15px rgba(30, 136, 229, 0.4);
    }
    
    .btn-print i {
        margin-right: 8px;
    }
    
    .btn-back {
        background-color: white;
        color: var(--text-dark);
        border: 2px solid var(--border-color);
        padding: 0.8rem 2rem;
        border-radius: 50px;
        font-weight: 600;
        display: flex;
        align-items: center;
        transition: all 0.3s ease;
    }
    
    .btn-back:hover {
        background-color: var(--background-light);
        color: var(--primary-color);
        border-color: var(--primary-light);
    }
    
    .btn-back i {
        margin-right: 8px;
    }
    
    .status-badge {
        display: inline-flex;
        align-items: center;
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 600;
    }
    
    .status-badge.pending {
        background-color: #FFF3E0;
        color: #E65100;
    }
    
    .status-badge.partial {
        background-color: #E1F5FE;
        color: #0288D1;
    }
    
    .status-badge.completed {
        background-color: #E8F5E9;
        color: #2E7D32;
    }
    
    .status-badge i {
        margin-right: 5px;
    }
    
    .payment-summary-row:last-child {
        margin-bottom: 0;
        padding-top: 0.8rem;
        bottom: 2.5rem;
        text-align: center;
        opacity: 0.8;
    }
    
    .receipt-qr img {
        max-width: 80px;
        border-radius: 5px;
        padding: 5px;
        background-color: white;
        box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    }
    
    .receipt-qr-text {
        font-size: 0.7rem;
        color: var(--text-color);
        margin-top: 0.3rem;
    }
    
    /* Note d'information - espacement réduit */
    .receipt-note {
        background-color: #FFF8E1;
        border-left: 3px solid var(--warning-color);
        padding: 0.5rem;
        border-radius: 4px;
        font-size: 0.7rem;
        color: #795548;
        margin-top: 0.75rem;
        display: flex;
        align-items: center;
    }
    
    .receipt-note i {
        font-size: 1rem;
        margin-right: 0.5rem;
        color: var(--warning-color);
    }
        
        .receipt-actions {
        margin-top: 2rem;
        display: flex;
        justify-content: center;
        gap: 1rem;
    }
    
    .btn-print {
        background-color: var(--primary-color);
        color: white;
        border: none;
        padding: 0.5rem 1rem;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.3s;
    }
    
    .btn-print:hover {
        background-color: var(--primary-dark);
    }
    
    @media print {
        @page {
            size: A5 portrait;
            margin: 0;
        }
        
        html, body {
            width: 148mm;
            height: 210mm;
            margin: 0;
            padding: 0;
            background-color: white;
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
            color-adjust: exact !important;
            overflow: hidden;
        }
        
        .container-fluid {
            padding: 0 !important;
            width: 148mm !important;
            max-width: 148mm !important;
            overflow: hidden;
        }
        
        .receipt-page {
            width: 148mm;
            height: 210mm;
            margin: 0;
            padding: 0;
            box-shadow: none;
            border: none;
            position: relative;
            page-break-after: always;
            page-break-inside: avoid;
            overflow: hidden;
        }
        
        .receipt-container {
            padding: 0;
            margin: 0;
            box-shadow: none;
        }
        
        /* Masquer les éléments de navigation lors de l'impression */
        .navbar, .sidebar, .breadcrumb, .receipt-actions, footer,
        .container-fluid h1, .container-fluid ol, .print-instructions,
        .btn, .alert, .d-print-none {
            display: none !important;
        }
        
        /* Optimisation des tailles pour tenir sur une page A5 */
        .receipt-header {
            padding: 0.2rem 0.3rem !important;
        }
        
        .receipt-logo-container {
            width: 40px !important;
            height: 40px !important;
            margin: 0 auto 0.2rem !important;
            padding: 3px !important;
        }
        
        .receipt-body {
            padding: 0.5rem !important;
        }
        
        .receipt-info-container {
            display: flex !important;
            flex-direction: row !important;
            justify-content: space-between !important;
            gap: 0.3rem !important;
            margin-bottom: 0.3rem !important;
        }
        
        .receipt-info-column {
            width: 49% !important;
        }
        
        .receipt-section {
            margin-bottom: 0.3rem !important;
            padding: 0.3rem !important;
        }
        
        .receipt-signatures {
            margin-top: 0.2rem !important;
            margin-bottom: 0.2rem !important;
            gap: 0.2rem !important;
        }
        
        .signature-box {
            padding: 0.3rem !important;
        }
        
        .signature-line {
            height: 12px !important;
            margin-bottom: 0.2rem !important;
        }
        
        .receipt-table {
            margin-bottom: 0.5rem !important;
        }
        
        .receipt-table th {
            padding: 0.2rem 0.2rem !important;
            font-size: 0.65rem !important;
        }
        
        .receipt-table td {
            padding: 0.2rem 0.2rem !important;
            font-size: 0.65rem !important;
        }
        
        .receipt-note {
            margin-top: 0.2rem !important;
            padding: 0.1rem !important;
            font-size: 0.55rem !important;
        }
        
        .terms-conditions {
            margin-top: 0.2rem !important;
            padding: 0.1rem !important;
            font-size: 0.55rem !important;
            line-height: 1 !important;
        }
        
        /* Pied de page supprimé */
        
        /* Réduire la taille des textes pour l'impression */
        .info-label, .info-value {
            font-size: 0.7rem !important;
        }
        
        .receipt-section-title {
            font-size: 0.8rem !important;
            margin-bottom: 0.3rem !important;
            padding-bottom: 0.3rem !important;
        }
        
        /* Assurer que les couleurs et dégradés s'impriment correctement */
        .receipt-header, .payment-summary, .receipt-table th,
        .badge, .status-badge, .payment-method-badge, .stamp-circle,
        .signature-box::before, .payment-summary::before {
            -webkit-print-color-adjust: exact !important;
            print-color-adjust: exact !important;
            color-adjust: exact !important;
        }
    }
</style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid px-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="mt-2">Reçu de paiement</h1>
        <div>
            <a href="<?php echo e(route('cashier.payments.index')); ?>" class="btn btn-outline-secondary me-2">
                <i class="fas fa-arrow-left"></i> Retour à la liste
            </a>
            <button class="btn btn-primary" onclick="window.print()">
                <i class="fas fa-print me-1"></i> Imprimer au format A5
            </button>
        </div>
    </div>
    
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="<?php echo e(route('cashier.dashboard')); ?>">Tableau de bord</a></li>
        <li class="breadcrumb-item"><a href="<?php echo e(route('cashier.payments.index')); ?>">Paiements</a></li>
        <li class="breadcrumb-item active">Reçu</li>
    </ol>
    
    <!-- Début de la zone imprimable au format A5 -->
    <div class="receipt-page">
        <div class="receipt-container">
            <div class="receipt-header">
                <div class="receipt-logo-container">
                    <img src="<?php echo e(asset('assets/images/logo_gradis.png')); ?>" alt="Logo" class="receipt-logo">
                </div>
                <h1 class="receipt-title">REÇU DE PAIEMENT</h1>
                <p class="receipt-number"># <?php echo e($payment->receipt_number ?? 'REC-' . str_pad($payment->id, 6, '0', STR_PAD_LEFT)); ?></p>
                <p class="receipt-date"><?php echo e($payment->payment_date->format('d/m/Y H:i')); ?></p>
            </div>
            
            <div class="receipt-body">
                <div class="receipt-info-container">
                    <div class="receipt-info-column">
                        <div class="receipt-section">
                            <h5 class="receipt-section-title"><i class="fas fa-user"></i> Informations client</h5>
                            <div class="info-row">
                                <div class="info-label">Nom:</div>
                                <div class="info-value"><?php echo e($payment->sale->customer_name); ?></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Téléphone:</div>
                                <div class="info-value"><?php echo e($payment->sale->customer_phone); ?></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Adresse:</div>
                                <div class="info-value"><?php echo e($payment->sale->customer_address); ?></div>
                            </div>
                        </div>
                    </div>
                    <div class="receipt-info-column">
                        <div class="receipt-section">
                            <h5 class="receipt-section-title"><i class="fas fa-shopping-cart"></i> Informations vente</h5>
                            <div class="info-row">
                                <div class="info-label">Référence:</div>
                                <div class="info-value"><?php echo e($payment->sale->invoice_number ?? 'VNT-' . str_pad($payment->sale->id, 6, '0', STR_PAD_LEFT)); ?></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Date de vente:</div>
                                <div class="info-value"><?php echo e($payment->sale->created_at->format('d/m/Y')); ?></div>
                            </div>
                            <div class="info-row">
                                <div class="info-label">Statut:</div>
                                <div class="info-value">
                                    <?php if($payment->sale->status === 'pending' || $payment->sale->status === 'pending_payment'): ?>
                                        <span class="status-badge pending"><i class="fas fa-clock"></i> En attente</span>
                                    <?php elseif($payment->sale->status === 'partial' || $payment->sale->status === 'partially_paid'): ?>
                                        <span class="status-badge partial"><i class="fas fa-sync"></i> Partiellement payé</span>
                                    <?php elseif($payment->sale->status === 'paid' || $payment->sale->status === 'completed'): ?>
                                        <span class="status-badge completed"><i class="fas fa-check-circle"></i> Payé</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="receipt-section">
                    <h5 class="receipt-section-title"><i class="fas fa-box"></i> Détails du produit</h5>
                    <table class="receipt-table">
                        <thead>
                            <tr>
                                <th>Produit</th>
                                <th>Quantité</th>
                                <th>Prix unitaire</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>
                                    <?php if($payment->sale->product): ?>
                                        <?php echo e($payment->sale->product->name); ?>

                                    <?php elseif($payment->sale->supply && $payment->sale->supply->details->isNotEmpty()): ?>
                                        <?php echo e($payment->sale->supply->details->first()->product->name); ?>

                                    <?php else: ?>
                                        N/A
                                    <?php endif; ?>
                                </td>
                                <td><?php echo e(number_format($payment->sale->quantity, 2, ',', ' ')); ?> T</td>
                                <td><?php echo e(number_format($payment->sale->unit_price, 0, ',', ' ')); ?> FCFA</td>
                                <td><?php echo e(number_format($payment->sale->total_amount, 0, ',', ' ')); ?> FCFA</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="receipt-info-container">
                    <div class="receipt-info-column">
                        <div class="receipt-section">
                            <h5 class="receipt-section-title"><i class="fas fa-credit-card"></i> Détails du paiement</h5>
                            <div class="info-row">
                                <div class="info-label">Mode de paiement:</div>
                                <div class="info-value">
                                    <span class="payment-method-badge">
                                        <?php switch($payment->payment_method):
                                            case ('cash'): ?>
                                                <i class="fas fa-money-bill-wave"></i> Espèces
                                                <?php break; ?>
                                            <?php case ('bank_transfer'): ?>
                                                <i class="fas fa-university"></i> Virement bancaire
                                                <?php break; ?>
                                            <?php case ('check'): ?>
                                                <i class="fas fa-money-check"></i> Chèque
                                                <?php break; ?>
                                            <?php case ('mobile_money'): ?>
                                                <i class="fas fa-mobile-alt"></i> Mobile Money
                                                <?php break; ?>
                                            <?php default: ?>
                                                <i class="fas fa-wallet"></i> Autre
                                        <?php endswitch; ?>
                                    </span>
                                </div>
                            </div>
                            <?php if($payment->reference_number): ?>
                            <div class="info-row">
                                <div class="info-label">Référence:</div>
                                <div class="info-value"><?php echo e($payment->reference_number); ?></div>
                            </div>
                            <?php endif; ?>
                            <div class="info-row">
                                <div class="info-label">Caissier:</div>
                                <div class="info-value"><?php echo e($payment->cashier->name ?? 'N/A'); ?></div>
                            </div>
                            
                            <?php if($payment->paymentSchedule): ?>
                            <div class="info-row">
                                <div class="info-label">Échéance:</div>
                                <div class="info-value"><?php echo e($payment->paymentSchedule->due_date->format('d/m/Y')); ?></div>
                            </div>
                            <?php endif; ?>
                            
                            <?php if($payment->notes): ?>
                            <div class="info-row">
                                <div class="info-label">Notes:</div>
                                <div class="info-value"><?php echo e($payment->notes); ?></div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="receipt-info-column">
                        <div class="payment-summary">
                            <div class="payment-summary-title">
                                <i class="fas fa-calculator"></i> Résumé financier
                            </div>
                            <div class="payment-summary-content">
                                <div class="payment-summary-row">
                                    <div>Montant total de la vente:</div>
                                    <div class="payment-summary-value"><?php echo e(number_format($payment->sale->total_amount, 0, ',', ' ')); ?> FCFA</div>
                                </div>
                                <div class="payment-summary-row">
                                    <div>Montant déjà payé:</div>
                                    <div class="payment-summary-value"><?php echo e(number_format($payment->sale->amount_paid - $payment->amount, 0, ',', ' ')); ?> FCFA</div>
                                </div>
                                <div class="payment-summary-row">
                                    <div>Montant de ce paiement:</div>
                                    <div class="payment-summary-value"><?php echo e(number_format($payment->amount, 0, ',', ' ')); ?> FCFA</div>
                                </div>
                                <div class="payment-summary-row">
                                    <div>Reste à payer:</div>
                                    <div class="payment-summary-value"><?php echo e(number_format($payment->sale->total_amount - $payment->sale->amount_paid, 0, ',', ' ')); ?> FCFA</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="receipt-signatures">
                    <div class="signature-box">
                        <div class="signature-line"></div>
                        <p class="signature-title">Signature du caissier</p>
                    </div>
                    <div class="signature-box stamp">
                        <div class="company-stamp">
                            <div class="stamp-circle">
                                <div class="stamp-text">GRADIS</div>
                                <div class="stamp-subtext">OFFICIEL</div>
                            </div>
                        </div>
                        <p class="signature-title">Cachet de l'entreprise</p>
                    </div>
                    <div class="signature-box">
                        <div class="signature-line"></div>
                        <p class="signature-title">Signature du client</p>
                    </div>
                </div>
                
                <div class="receipt-note">
                    <i class="fas fa-info-circle"></i>
                    <div>
                        <strong>Info :</strong> Preuve officielle de paiement. À conserver pour réclamations.
                    </div>
                </div>
                
                <div class="receipt-qr">
                    <img src="https://api.qrserver.com/v1/create-qr-code/?size=100x100&data=<?php echo e(urlencode(route('cashier.payments.receipt', $payment->id))); ?>" alt="QR Code">
                    <p class="receipt-qr-text">Scannez pour vérifier l'authenticité</p>
                </div>
                
                <div class="terms-conditions">
                    <p class="terms-conditions-title">Termes et Conditions</p>
                    <p>Valable avec cachet. Remboursement sous 7 jours. Réclamation avec reçu original uniquement.</p>
                </div>
            </div>
            
            <!-- Pied de page supprimé -->
        </div>
    </div>
    </div>
    
    <!-- Message d'aide pour l'impression -->
    <div class="print-instructions mt-4 mb-5 d-print-none">
        <div class="alert alert-info">
            <i class="fas fa-info-circle me-2"></i>
            <strong>Astuce d'impression :</strong> Pour un résultat optimal, assurez-vous que votre imprimante est configurée au format A5 sans marges.
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('scripts'); ?>
<script>
    // Script pour optimiser l'impression
    document.querySelector('.btn-primary').addEventListener('click', function() {
        setTimeout(function() {
            window.print();
        }, 300);
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.print', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/cashier/payments/receipt.blade.php ENDPATH**/ ?>
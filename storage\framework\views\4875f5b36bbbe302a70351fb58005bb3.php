<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <title><?php echo $__env->yieldContent('title', 'Caissier'); ?> - GRADIS</title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo e(asset('favicon.ico')); ?>">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2196F3;
            --secondary-color: #475569;
            --success-color: #4CAF50;
            --warning-color: #FF9800;
            --danger-color: #F44336;
            --light-color: #f1f5f9;
            --dark-color: #0f172a;
            
            /* Variables pour l'interface moderne des ventes */
            --primary-light: #BBDEFB;
            --primary-dark: #1976D2;
            --info-color: #00BCD4;
            --text-color: #37474F;
            --border-radius: 0.5rem;
            --card-radius: 0.75rem;
            --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            --transition: all 0.25s ease;
            
            /* États de paiement */
            --paid-color: #4CAF50;
            --pending-color: #FF9800;
            --failed-color: #F44336;
        }
        
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8fafc;
        }
        
        /* Sidebar */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            height: 100vh;
            width: 250px;
            background: var(--dark-color);
            color: white;
            transition: all 0.3s ease;
            z-index: 1000;
            box-shadow: 4px 0 10px rgba(0,0,0,0.1);
        }
        
        .sidebar-header {
            padding: 1.5rem;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar-menu {
            padding: 1rem 0;
        }
        
        .menu-item {
            padding: 0.75rem 1.5rem;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            display: flex;
            align-items: center;
            transition: all 0.3s ease;
        }
        
        .menu-item:hover, .menu-item.active {
            color: white;
            background: rgba(255,255,255,0.1);
        }
        
        .menu-item i {
            width: 20px;
            margin-right: 10px;
        }
        
        /* Main Content */
        .main-content {
            margin-left: 250px;
            padding: 2rem;
            min-height: 100vh;
        }
        
        /* Top Navbar */
        .top-navbar {
            position: fixed;
            top: 0;
            right: 0;
            left: 250px;
            height: 60px;
            background: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            z-index: 999;
            padding: 0 2rem;
        }
        
        .navbar-content {
            height: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .navbar-right {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .profile-menu {
            position: relative;
        }
        
        .profile-button {
            background: none;
            border: none;
            color: var(--dark-color);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            cursor: pointer;
        }
        
        .profile-dropdown {
            position: absolute;
            top: 100%;
            right: 0;
            width: 250px;
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            padding: 0.5rem 0;
            display: none;
        }
        
        .profile-dropdown.show {
            display: block;
        }
        
        .dropdown-item {
            padding: 0.5rem 1rem;
            color: var(--dark-color);
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .dropdown-item:hover {
            background: var(--light-color);
        }
        
        /* Cards */
        .card {
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            border-radius: 0.5rem;
        }
        
        .card-header {
            background: white;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }
        
        /* Utilities */
        .border-left-primary { border-left: 4px solid var(--primary-color); }
        .border-left-success { border-left: 4px solid var(--success-color); }
        .border-left-info { border-left: 4px solid var(--secondary-color); }
        .border-left-warning { border-left: 4px solid var(--warning-color); }
    </style>
    
    <!-- Styles personnalisés pour la vue moderne des ventes -->
    <link rel="stylesheet" href="<?php echo e(asset('css/modern-sales-view.css')); ?>">
    <!-- Styles pour les filtres -->
    <link rel="stylesheet" href="<?php echo e(asset('css/filter-styles.css')); ?>">
    <!-- Animations pour l'interface moderne -->
    <link rel="stylesheet" href="<?php echo e(asset('css/animations.css')); ?>">
    <!-- Polices pour l'interface moderne -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Roboto+Mono&display=swap">
    
    <!-- Styles personnalisés pour chaque vue -->
    <?php echo $__env->yieldContent('styles'); ?>
    
    <!-- Styles complémentaires pour l'interface moderne -->
    <style>
        /* Styles de base pour l'interface moderne */
        body {
            font-family: 'Inter', sans-serif;
        }
        
        .container-fluid {
            padding: 1.5rem;
            max-width: 1400px;
            margin: 0 auto;
        }
        
        /* Carte principale */
        .card {
            background-color: white;
            border-radius: var(--card-radius);
            box-shadow: var(--box-shadow);
            border: none;
            margin-bottom: 1.5rem;
            overflow: hidden;
        }
        
        /* En-tête de page */
        .page-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1.5rem;
        }
        
        .page-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--dark-color);
            display: flex;
            align-items: center;
            gap: 0.5rem;
            margin: 0;
        }
        
        .page-title i {
            color: var(--primary-color);
        }
        
        /* Stats en haut de page */
        .stats-container {
            display: flex;
            gap: 1rem;
            margin-bottom: 1.5rem;
            flex-wrap: wrap;
        }
        
        .stat-card {
            background-color: white;
            border-radius: var(--card-radius);
            box-shadow: var(--box-shadow);
            padding: 1.25rem;
            flex: 1;
            min-width: 200px;
            display: flex;
            flex-direction: column;
        }
        
        .stat-title {
            font-size: 0.85rem;
            color: var(--text-color);
            opacity: 0.8;
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .stat-title i {
            color: var(--primary-color);
        }
        
        .stat-value {
            font-size: 1.5rem;
            font-weight: 700;
            color: var(--dark-color);
            margin-bottom: 0.25rem;
        }
        
        .stat-trend {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .stat-trend.positive {
            color: var(--paid-color);
        }
        
        .stat-trend.negative {
            color: var(--failed-color);
        }
    </style>
    
    <?php echo $__env->yieldPushContent('styles'); ?>
</head>
<body style="background-color: #ffffff !important;">
    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <img src="<?php echo e(asset('assets/images/logo_mini_gradis.png')); ?>" alt="GRADIS Logo" class="img-fluid" style="height: 50px; width: auto;">
        </div>
        
        <div class="sidebar-menu">
            <a href="<?php echo e(route('cashier.dashboard')); ?>" class="menu-item <?php echo e(request()->routeIs('cashier.dashboard') ? 'active' : ''); ?>">
                <i class="fas fa-tachometer-alt"></i>
                <span>Tableau de bord</span>
            </a>
            


            <a href="<?php echo e(route('cashier.credit-sales.index')); ?>" class="menu-item <?php echo e(request()->routeIs('cashier.credit-sales.*') ? 'active' : ''); ?>">
                <i class="fas fa-credit-card"></i>
                <span>Ventes à Crédit</span>
            </a>
            
            <a href="<?php echo e(route('cashier.sales.index')); ?>" class="menu-item <?php echo e(request()->routeIs('cashier.sales.*') ? 'active' : ''); ?>">
                <i class="fas fa-shopping-cart"></i>
                <span>Ventes Directes</span>
            </a>
            
            <a href="<?php echo e(route('cashier.payments.index')); ?>" class="menu-item <?php echo e(request()->routeIs('cashier.payments.*') ? 'active' : ''); ?>">
                <i class="fas fa-money-bill-wave"></i>
                <span>Paiements</span>
            </a>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content" id="main-content">
        <!-- Top Navbar -->
        <nav class="top-navbar">
            <div class="navbar-content">
                <div class="navbar-left d-flex align-items-center">
                    <!-- Bouton pour afficher/masquer la sidebar sur mobile -->
                    <button id="sidebarToggleBtn" class="sidebar-toggle-btn me-3 d-md-none">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h4 class="mb-0"><?php echo $__env->yieldContent('title', 'Tableau de bord'); ?></h4>
                </div>
                <div class="navbar-right">
                    <div class="profile-menu">
                        <button class="profile-button" onclick="toggleProfileMenu()">
                            <span><?php echo e(Auth::user()->name); ?></span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="profile-dropdown" id="profileDropdown">
                            <a href="<?php echo e(route('cashier.profile')); ?>" class="dropdown-item">
                                <i class="fas fa-user"></i>
                                <span>Mon profil</span>
                            </a>
                            <form action="<?php echo e(route('logout')); ?>" method="POST">
                                <?php echo csrf_field(); ?>
                                <button type="submit" class="dropdown-item text-danger">
                                    <i class="fas fa-sign-out-alt"></i>
                                    <span>Déconnexion</span>
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Page Content -->
        <div style="padding-top: 60px;">
            <?php if(session('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo e(session('success')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo e(session('error')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php echo $__env->yieldContent('content'); ?>
        </div>
        
        <!-- Footer -->
        <footer class="admin-footer">
            <div class="text-center py-3">
                2025 GRADIS. Tous droits réservés. Développé par MOMK-Solutions
            </div>
        </footer>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script>
        function toggleProfileMenu() {
            document.getElementById('profileDropdown').classList.toggle('show');
        }

        // Fermer le menu profil si on clique en dehors
        window.onclick = function(event) {
            if (!event.target.matches('.profile-button') && !event.target.matches('.profile-button *')) {
                var dropdowns = document.getElementsByClassName('profile-dropdown');
                for (var i = 0; i <dropdowns.length; i++) {
                    var openDropdown = dropdowns[i];
                    if (openDropdown.classList.contains('show')) {
                        openDropdown.classList.remove('show');
                    }
                }
            }
        }
        
        // Gestion de la sidebar responsive
        document.addEventListener('DOMContentLoaded', function() {
            // Afficher le bouton de bascule sur mobile
            const sidebarToggleBtn = document.getElementById('sidebarToggleBtn');
            if (sidebarToggleBtn) {
                // Ajouter l'événement de clic pour basculer la sidebar
                sidebarToggleBtn.addEventListener('click', function() {
                    const sidebar = document.getElementById('sidebar');
                    
                    // Basculer les classes pour la sidebar et le body
                    sidebar.classList.toggle('sidebar-hidden');
                    document.body.classList.toggle('sidebar-hidden-body');
                    
                    // Changer l'icône du bouton
                    if (sidebar.classList.contains('sidebar-hidden')) {
                        sidebarToggleBtn.innerHTML = '<i class="fas fa-bars"></i>';
                    } else {
                        sidebarToggleBtn.innerHTML = '<i class="fas fa-times"></i>';
                    }
                });
                
                // Vérifier si on est sur mobile et cacher automatiquement la sidebar au chargement
                if (window.innerWidth <= 576) {
                    const sidebar = document.getElementById('sidebar');
                    sidebar.classList.add('sidebar-hidden');
                    document.body.classList.add('sidebar-hidden-body');
                }
            }
            
            // Ajouter un écouteur de redimensionnement pour ajuster la sidebar
            window.addEventListener('resize', function() {
                const sidebar = document.getElementById('sidebar');
                
                // Si on passe en mode desktop, rétablir la sidebar
                if (window.innerWidth > 768 && sidebar.classList.contains('sidebar-hidden')) {
                    sidebar.classList.remove('sidebar-hidden');
                    document.body.classList.remove('sidebar-hidden-body');
                    if (sidebarToggleBtn) {
                        sidebarToggleBtn.innerHTML = '<i class="fas fa-bars"></i>';
                    }
                }
            });
        });
    </script>
    
    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\gradis\resources\views/layouts/cashier.blade.php ENDPATH**/ ?>
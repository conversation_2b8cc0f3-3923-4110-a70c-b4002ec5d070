<?php $__env->startSection('title', 'Gestion des Ventes'); ?>

<?php $__env->startPush('styles'); ?>
<link href="<?php echo e(asset('css/sales-admin.css')); ?>" rel="stylesheet">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <!-- Header Section avec statistiques -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-3">
                <div>
                    <h1 class="display-6 fw-bold text-primary mb-1">
                        <i class="fas fa-chart-line me-2"></i>Gestion des Ventes
                    </h1>
                    <p class="text-muted mb-0">Tableau de bord des ventes et transactions</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" data-bs-toggle="modal" data-bs-target="#filterModal">
                        <i class="fas fa-filter me-1"></i>Filtres avancés
                    </button>
                    <button class="btn btn-primary" onclick="exportSales()">
                        <i class="fas fa-download me-1"></i>Exporter
                    </button>
                </div>
            </div>
        </div>
    </div>

    <?php echo $__env->make('admin.sales.partials.statistics', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    
    <?php if(isset($pendingDiscountSales) && $pendingDiscountSales->count() > 0): ?>
        <?php echo $__env->make('admin.sales.partials.pending_discount_sales', ['pendingDiscountSales' => $pendingDiscountSales], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>

    
    <?php if(isset($pendingPriceIncreaseSales) && $pendingPriceIncreaseSales->count() > 0): ?>
        <?php echo $__env->make('admin.sales.partials.pending_price_increase_sales', ['pendingPriceIncreaseSales' => $pendingPriceIncreaseSales], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php endif; ?>

    <!-- Barre de recherche et filtres -->
    <div class="card border-0 shadow-sm mb-4 search-filter-card">
        <div class="card-body">
            <div class="row g-3">
                <div class="col-md-4">
                    <div class="search-box">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" id="searchInput" class="form-control ps-5" placeholder="Rechercher par ID, produit, client...">
                    </div>
                </div>
                <div class="col-md-3">
                    <select id="statusFilter" class="form-select">
                        <option value="">Tous les statuts</option>
                        <option value="En attente">En attente</option>
                        <option value="Payé">Payé</option>
                        <option value="Annulé">Annulé</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select id="dateFilter" class="form-select">
                        <option value="">Toutes les dates</option>
                        <option value="today">Aujourd'hui</option>
                        <option value="week">Cette semaine</option>
                        <option value="month">Ce mois</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button class="btn btn-outline-secondary w-100" onclick="resetFilters()">
                        <i class="fas fa-undo me-1"></i>Reset
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Tableau des ventes -->
    <div class="card border-0 shadow-sm main-table-card">
        <div class="card-header bg-white border-bottom">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2 text-primary"></i>Liste des Ventes
                </h5>
                <div class="d-flex align-items-center gap-2">
                    <span class="badge bg-light text-dark border border-primary" style="border: 2px solid #667eea !important; box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3); font-weight: 600;"><?php echo e($sales->total()); ?> ventes</span>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-secondary" onclick="toggleView('table')" id="tableViewBtn">
                            <i class="fas fa-table"></i>
                        </button>
                        <button class="btn btn-outline-secondary" onclick="toggleView('grid')" id="gridViewBtn">
                            <i class="fas fa-th-large"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <!-- Vue tableau -->
            <div id="tableView" class="table-responsive">
                <table class="table table-hover align-middle mb-0 modern-table">
                    <thead class="table-light">
                        <tr>
                            <th class="border-0 ps-4">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll">
                                </div>
                            </th>
                            <th class="border-0">Vente</th>
                            <th class="border-0">Date & Heure</th>
                            <th class="border-0">Produit</th>
                            <th class="border-0">Client</th>
                            <th class="border-0">Quantité</th>
                            <th class="border-0">Montant</th>
                            <th class="border-0">Statut</th>
                            <th class="border-0 text-center">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $sales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr class="sale-row" data-sale-id="<?php echo e($sale->id); ?>">
                                <td class="ps-4">
                                    <div class="form-check">
                                        <input class="form-check-input sale-checkbox" type="checkbox" value="<?php echo e($sale->id); ?>">
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="sale-id-badge">
                                            #<?php echo e(str_pad($sale->id, 4, '0', STR_PAD_LEFT)); ?>

                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="date-info">
                                        <div class="fw-semibold"><?php echo e($sale->created_at->format('d/m/Y')); ?></div>
                                        <small class="text-muted"><?php echo e($sale->created_at->format('H:i')); ?></small>
                                    </div>
                                </td>
                                <td>
                                    <div class="product-info">
                                        <?php if($sale->product): ?>
                                            <div class="fw-semibold"><?php echo e($sale->product->name); ?></div>
                                            <small class="text-muted"><?php echo e($sale->product->category->name ?? 'N/A'); ?></small>
                                        <?php else: ?>
                                            <span class="text-danger">
                                                <i class="fas fa-exclamation-triangle me-1"></i>Produit non trouvé
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="customer-info">
                                        <div class="fw-semibold"><?php echo e($sale->customer_name ?? 'N/A'); ?></div>
                                        <?php if($sale->customer_phone): ?>
                                            <small class="text-muted"><?php echo e($sale->customer_phone); ?></small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <div class="quantity-badge">
                                        <span class="badge bg-light text-dark"><?php echo e($sale->quantity); ?> T</span>
                                    </div>
                                </td>
                                <td>
                                    <div class="amount-info">
                                        <div class="fw-bold text-primary">
                                            <?php echo e(number_format($sale->quantity * $sale->unit_price - ($sale->discount_total ?? 0), 0, ',', ' ')); ?> FCFA
                                        </div>
                                        <?php if($sale->discount_total > 0): ?>
                                            <small class="text-success">
                                                <i class="fas fa-tag me-1"></i>-<?php echo e(number_format($sale->discount_total, 0, ',', ' ')); ?> FCFA
                                            </small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <?php switch($sale->payment_status):
                                        case ('pending'): ?>
                                            <span class="status-badge status-pending">
                                                <i class="fas fa-clock me-1"></i>En attente
                                            </span>
                                            <?php break; ?>
                                        <?php case ('paid'): ?>
                                            <span class="status-badge status-paid">
                                                <i class="fas fa-check-circle me-1"></i>Payé
                                            </span>
                                            <?php break; ?>
                                        <?php case ('cancelled'): ?>
                                            <span class="status-badge status-cancelled">
                                                <i class="fas fa-times-circle me-1"></i>Annulé
                                            </span>
                                            <?php break; ?>
                                    <?php endswitch; ?>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?php echo e(route('admin.sales.show', $sale)); ?>"
                                               class="btn btn-outline-primary"
                                               data-bs-toggle="tooltip"
                                               title="Voir les détails">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button class="btn btn-outline-warning btn-sm"
                                                    onclick="editSaleDate(<?php echo e($sale->id); ?>, '<?php echo e($sale->created_at->format('Y-m-d')); ?>', '<?php echo e($sale->created_at->format('H:i')); ?>')"
                                                    data-bs-toggle="tooltip"
                                                    title="Modifier la date">
                                                <i class="fas fa-calendar-alt me-1"></i>Date
                                            </button>
                                            <button class="btn btn-outline-info"
                                                    onclick="printInvoice(<?php echo e($sale->id); ?>)"
                                                    data-bs-toggle="tooltip"
                                                    title="Imprimer facture">
                                                <i class="fas fa-print"></i>
                                            </button>
                                            <?php if($sale->status !== 'cancelled'): ?>
                                            <button class="btn btn-outline-warning"
                                                    onclick="cancelSale(<?php echo e($sale->id); ?>, '<?php echo e($sale->customer_name); ?>', <?php echo e($sale->quantity); ?>)"
                                                    data-bs-toggle="tooltip"
                                                    title="Annuler la vente">
                                                <i class="fas fa-ban"></i>
                                            </button>
                                            <?php endif; ?>
                                            <button class="btn btn-outline-danger"
                                                    onclick="deleteSale(<?php echo e($sale->id); ?>)"
                                                    data-bs-toggle="tooltip"
                                                    title="Supprimer">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="9" class="text-center py-5">
                                    <div class="empty-state">
                                        <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">Aucune vente trouvée</h5>
                                        <p class="text-muted">Les ventes apparaîtront ici une fois créées.</p>
                                    </div>
                                </td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <!-- Vue grille (cachée par défaut) -->
            <div id="gridView" class="d-none p-4">
                <div class="row g-3">
                    <?php $__empty_1 = true; $__currentLoopData = $sales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <div class="col-xl-4 col-lg-6 col-md-6">
                            <div class="sale-card" data-status="<?php echo e($sale->payment_status); ?>">
                                <div class="sale-card-header">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="sale-id">#<?php echo e(str_pad($sale->id, 4, '0', STR_PAD_LEFT)); ?></span>
                                        <?php switch($sale->payment_status):
                                            case ('pending'): ?>
                                                <span class="status-badge status-pending">En attente</span>
                                                <?php break; ?>
                                            <?php case ('paid'): ?>
                                                <span class="status-badge status-paid">Payé</span>
                                                <?php break; ?>
                                            <?php case ('cancelled'): ?>
                                                <span class="status-badge status-cancelled">Annulé</span>
                                                <?php break; ?>
                                        <?php endswitch; ?>
                                    </div>
                                </div>
                                <div class="sale-card-body">
                                    <div class="mb-2">
                                        <small class="text-muted">Produit</small>
                                        <div class="fw-semibold"><?php echo e($sale->product->name ?? 'N/A'); ?></div>
                                    </div>
                                    <div class="mb-2">
                                        <small class="text-muted">Client</small>
                                        <div class="fw-semibold"><?php echo e($sale->customer_name ?? 'N/A'); ?></div>
                                    </div>
                                    <div class="row">
                                        <div class="col-6">
                                            <small class="text-muted">Quantité</small>
                                            <div class="fw-semibold"><?php echo e($sale->quantity); ?> T</div>
                                        </div>
                                        <div class="col-6">
                                            <small class="text-muted">Montant</small>
                                            <div class="fw-bold text-primary">
                                                <?php echo e(number_format($sale->quantity * $sale->unit_price - ($sale->discount_total ?? 0), 0, ',', ' ')); ?> FCFA
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="sale-card-footer">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <small class="text-muted"><?php echo e($sale->created_at->format('d/m/Y H:i')); ?></small>
                                        <div class="btn-group btn-group-sm">
                                            <a href="<?php echo e(route('admin.sales.show', $sale)); ?>" class="btn btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <button class="btn btn-outline-warning btn-sm"
                                                    onclick="editSaleDate(<?php echo e($sale->id); ?>, '<?php echo e($sale->created_at->format('Y-m-d')); ?>', '<?php echo e($sale->created_at->format('H:i')); ?>')"
                                                    data-bs-toggle="tooltip"
                                                    title="Modifier la date">
                                                <i class="fas fa-calendar-alt"></i>
                                            </button>
                                            <?php if($sale->status !== 'cancelled'): ?>
                                            <button class="btn btn-outline-warning btn-sm"
                                                    onclick="cancelSale(<?php echo e($sale->id); ?>, '<?php echo e($sale->customer_name); ?>', <?php echo e($sale->quantity); ?>)"
                                                    data-bs-toggle="tooltip"
                                                    title="Annuler la vente">
                                                <i class="fas fa-ban"></i>
                                            </button>
                                            <?php endif; ?>
                                            <button class="btn btn-outline-danger" onclick="deleteSale(<?php echo e($sale->id); ?>)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <div class="col-12">
                            <div class="empty-state text-center py-5">
                                <i class="fas fa-shopping-cart fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">Aucune vente trouvée</h5>
                                <p class="text-muted">Les ventes apparaîtront ici une fois créées.</p>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        <?php if($sales->hasPages()): ?>
            <div class="card-footer bg-white border-top">
                <div class="d-flex justify-content-between align-items-center">
                    <div class="pagination-info">
                        <small class="text-muted">
                            Affichage de <?php echo e($sales->firstItem()); ?> à <?php echo e($sales->lastItem()); ?> sur <?php echo e($sales->total()); ?> résultats
                        </small>
                    </div>
                    <div class="pagination-wrapper">
                        <?php echo e($sales->links()); ?>

                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>

<!-- Modal de filtres avancés -->
<div class="modal fade" id="filterModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content advanced-filter-modal">
            <div class="modal-header gradient-header">
                <div class="d-flex align-items-center">
                    <div class="modal-icon">
                        <i class="fas fa-sliders-h"></i>
                    </div>
                    <div class="ms-3">
                        <h5 class="modal-title mb-0">Filtres Avancés</h5>
                        <small class="text-white-50">Affinez votre recherche avec des critères précis</small>
                    </div>
                </div>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body p-4">
                <form id="advancedFilterForm">
                    <!-- Section Période -->
                    <div class="filter-section mb-4">
                        <div class="section-header">
                            <i class="fas fa-calendar-alt section-icon"></i>
                            <h6 class="section-title">Période de recherche</h6>
                        </div>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="date" class="form-control" id="startDate" placeholder="Date de début">
                                    <label for="startDate">
                                        <i class="fas fa-calendar-plus me-2"></i>Date de début
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="date" class="form-control" id="endDate" placeholder="Date de fin">
                                    <label for="endDate">
                                        <i class="fas fa-calendar-minus me-2"></i>Date de fin
                                    </label>
                                </div>
                            </div>
                        </div>
                        <!-- Boutons de période rapide -->
                        <div class="quick-period-buttons mt-3">
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickPeriod('today')">
                                Aujourd'hui
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickPeriod('week')">
                                Cette semaine
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickPeriod('month')">
                                Ce mois
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" onclick="setQuickPeriod('quarter')">
                                Ce trimestre
                            </button>
                        </div>
                    </div>

                    <!-- Section Montants -->
                    <div class="filter-section mb-4">
                        <div class="section-header">
                            <i class="fas fa-coins section-icon"></i>
                            <h6 class="section-title">Fourchette de montants (FCFA)</h6>
                        </div>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="number" class="form-control" id="minAmount" placeholder="Montant minimum" min="0">
                                    <label for="minAmount">
                                        <i class="fas fa-arrow-up me-2"></i>Montant minimum
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="number" class="form-control" id="maxAmount" placeholder="Montant maximum" min="0">
                                    <label for="maxAmount">
                                        <i class="fas fa-arrow-down me-2"></i>Montant maximum
                                    </label>
                                </div>
                            </div>
                        </div>
                        <!-- Slider de montant -->
                        <div class="amount-slider mt-3">
                            <label class="form-label">Ou utilisez le curseur :</label>
                            <div class="range-slider">
                                <input type="range" class="form-range" id="amountRange" min="0" max="10000000" step="50000" value="0">
                                <div class="range-labels">
                                    <span>0 FCFA</span>
                                    <span id="rangeValue">0 FCFA</span>
                                    <span>10M FCFA</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Section Produits et Statuts -->
                    <div class="filter-section mb-4">
                        <div class="section-header">
                            <i class="fas fa-boxes section-icon"></i>
                            <h6 class="section-title">Produits et Statuts</h6>
                        </div>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <select class="form-select" id="productFilter">
                                        <option value="">Tous les produits</option>
                                        <?php $__currentLoopData = $sales->pluck('product')->unique()->filter(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($product->name); ?>"><?php echo e($product->name); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <label for="productFilter">
                                        <i class="fas fa-cube me-2"></i>Sélectionner un produit
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <select class="form-select" id="statusFilterAdvanced">
                                        <option value="">Tous les statuts</option>
                                        <option value="paid">Payé</option>
                                        <option value="pending">En attente</option>
                                        <option value="cancelled">Annulé</option>
                                    </select>
                                    <label for="statusFilterAdvanced">
                                        <i class="fas fa-flag me-2"></i>Statut de paiement
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Section Client -->
                    <div class="filter-section">
                        <div class="section-header">
                            <i class="fas fa-users section-icon"></i>
                            <h6 class="section-title">Informations client</h6>
                        </div>
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="text" class="form-control" id="customerName" placeholder="Nom du client">
                                    <label for="customerName">
                                        <i class="fas fa-user me-2"></i>Nom du client
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    <input type="tel" class="form-control" id="customerPhone" placeholder="Téléphone">
                                    <label for="customerPhone">
                                        <i class="fas fa-phone me-2"></i>Téléphone
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer gradient-footer">
                <div class="d-flex justify-content-between w-100">
                    <div>
                        <button type="button" class="btn btn-outline-light" onclick="resetAdvancedFilters()">
                            <i class="fas fa-undo me-2"></i>Réinitialiser
                        </button>
                    </div>
                    <div>
                        <button type="button" class="btn btn-light me-2" data-bs-dismiss="modal">
                            <i class="fas fa-times me-2"></i>Annuler
                        </button>
                        <button type="button" class="btn btn-warning" onclick="applyAdvancedFilters()">
                            <i class="fas fa-search me-2"></i>Appliquer les filtres
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php echo $__env->make('admin.sales.partials.bulk-actions', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
</div>

<!-- Footer -->
<footer class="footer mt-5 py-4 bg-light border-top">
    <div class="container text-center">
        <span class="text-muted"> <?php echo e(date('Y')); ?> GRADIS. Tous droits réservés. Développé par MOMK-Solutions</span>
    </div>
</footer>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Styles personnalisés pour l'interface des ventes */
.sales-stat-card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border-radius: 12px;
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, #667eea, #764ba2) border-box;
}

.sales-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2) !important;
    border: 2px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, #764ba2, #667eea) border-box;
}

/* Contours colorés spécifiques pour chaque carte de statistique */
.sales-stat-card:nth-child(1) {
    border: 3px solid #667eea;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.2);
    position: relative;
    overflow: hidden;
}

.sales-stat-card:nth-child(1)::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
    background-size: 200% 100%;
    animation: gradientShift 3s ease-in-out infinite;
}

.sales-stat-card:nth-child(1):hover {
    border-color: #5a67d8;
    box-shadow: 0 12px 35px rgba(102, 126, 234, 0.3);
    transform: translateY(-5px) scale(1.02);
}

.sales-stat-card:nth-child(2) {
    border: 3px solid #28a745;
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.2);
    position: relative;
    overflow: hidden;
}

.sales-stat-card:nth-child(2)::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #28a745, #20c997, #28a745);
    background-size: 200% 100%;
    animation: gradientShift 3.5s ease-in-out infinite;
}

.sales-stat-card:nth-child(2):hover {
    border-color: #1e7e34;
    box-shadow: 0 12px 35px rgba(40, 167, 69, 0.3);
    transform: translateY(-5px) scale(1.02);
}

.sales-stat-card:nth-child(3) {
    border: 3px solid #ffc107;
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.2);
    position: relative;
    overflow: hidden;
}

.sales-stat-card:nth-child(3)::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #ffc107, #fd7e14, #ffc107);
    background-size: 200% 100%;
    animation: gradientShift 2.8s ease-in-out infinite;
}

.sales-stat-card:nth-child(3):hover {
    border-color: #e0a800;
    box-shadow: 0 12px 35px rgba(255, 193, 7, 0.3);
    transform: translateY(-5px) scale(1.02);
}

.sales-stat-card:nth-child(4) {
    border: 3px solid #17a2b8;
    box-shadow: 0 6px 20px rgba(23, 162, 184, 0.2);
    position: relative;
    overflow: hidden;
}

.sales-stat-card:nth-child(4)::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #17a2b8, #007bff, #17a2b8);
    background-size: 200% 100%;
    animation: gradientShift 4s ease-in-out infinite;
}

.sales-stat-card:nth-child(4):hover {
    border-color: #138496;
    box-shadow: 0 12px 35px rgba(23, 162, 184, 0.3);
    transform: translateY(-5px) scale(1.02);
}

/* Animation de pulsation pour les icônes */
.sales-stat-card .stat-icon {
    transition: all 0.3s ease;
}

.sales-stat-card:hover .stat-icon {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

/* Effet de brillance sur les cartes */
.sales-stat-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s;
}

.sales-stat-card:hover::after {
    left: 100%;
}

/* Effets de lueur spécifiques pour chaque carte */
.sales-stat-card:nth-child(1):hover {
    box-shadow:
        0 12px 35px rgba(102, 126, 234, 0.3),
        0 0 0 1px rgba(102, 126, 234, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.sales-stat-card:nth-child(2):hover {
    box-shadow:
        0 12px 35px rgba(40, 167, 69, 0.3),
        0 0 0 1px rgba(40, 167, 69, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.sales-stat-card:nth-child(3):hover {
    box-shadow:
        0 12px 35px rgba(255, 193, 7, 0.3),
        0 0 0 1px rgba(255, 193, 7, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.sales-stat-card:nth-child(4):hover {
    box-shadow:
        0 12px 35px rgba(23, 162, 184, 0.3),
        0 0 0 1px rgba(23, 162, 184, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* Animation de pulsation pour attirer l'attention */
@keyframes stat-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.02); }
    100% { transform: scale(1); }
}

.sales-stat-card {
    animation: stat-pulse 4s ease-in-out infinite;
}

.sales-stat-card:nth-child(1) { animation-delay: 0s; }
.sales-stat-card:nth-child(2) { animation-delay: 1s; }
.sales-stat-card:nth-child(3) { animation-delay: 2s; }
.sales-stat-card:nth-child(4) { animation-delay: 3s; }

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid rgba(255,255,255,0.3);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255,255,255,0.1);
    border-radius: 15px;
    transform: scale(0);
    transition: transform 0.3s ease;
}

.sales-stat-card:hover .stat-icon::before {
    transform: scale(1);
}

.stat-value {
    font-size: 1.8rem;
    font-weight: 800;
    color: #1a202c;
    line-height: 1.2;
    text-shadow: 0 1px 3px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.stat-label {
    font-size: 0.9rem;
    color: #4a5568;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.sales-stat-card:hover .stat-value {
    transform: scale(1.05);
    color: #2d3748;
}

.sales-stat-card:hover .stat-label {
    color: #2d3748;
    font-weight: 700;
}

/* Badges colorés pour les statistiques */
.percentage-badge {
    display: inline-block;
    padding: 0.35rem 0.7rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    border: 2px solid;
    box-shadow: 0 3px 8px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.percentage-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    transition: left 0.5s;
}

.percentage-badge:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 6px 15px rgba(0,0,0,0.2);
}

.percentage-badge:hover::before {
    left: 100%;
}

.percentage-badge.success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border-color: #28a745;
}

.percentage-badge.success:hover {
    background: linear-gradient(135deg, #c3e6cb 0%, #d4edda 100%);
    border-color: #1e7e34;
}

.percentage-badge.info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
    border-color: #17a2b8;
}

.percentage-badge.info:hover {
    background: linear-gradient(135deg, #bee5eb 0%, #d1ecf1 100%);
    border-color: #138496;
}

.percentage-badge.warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    border-color: #ffc107;
}

.percentage-badge.warning:hover {
    background: linear-gradient(135deg, #ffeaa7 0%, #fff3cd 100%);
    border-color: #e0a800;
}

.percentage-badge.primary {
    background: linear-gradient(135deg, #cce7ff 0%, #b3d9ff 100%);
    color: #004085;
    border-color: #007bff;
}

.percentage-badge.primary:hover {
    background: linear-gradient(135deg, #b3d9ff 0%, #cce7ff 100%);
    border-color: #0056b3;
}

.search-box {
    position: relative;
}

.search-icon {
    position: absolute;
    left: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #a0aec0;
    z-index: 10;
}

/* Contours colorés pour les cartes principales */
.search-filter-card {
    border: 2px solid #e2e8f0 !important;
    border-radius: 15px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.search-filter-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c);
    background-size: 300% 100%;
    animation: gradientShift 3s ease-in-out infinite;
}

.search-filter-card:hover {
    border-color: #667eea !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
    transform: translateY(-2px);
}

.main-table-card {
    border: 2px solid #e2e8f0 !important;
    border-radius: 15px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.main-table-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #17a2b8, #28a745, #ffc107, #dc3545);
    background-size: 300% 100%;
    animation: gradientShift 4s ease-in-out infinite;
}

.main-table-card:hover {
    border-color: #17a2b8 !important;
    box-shadow: 0 8px 25px rgba(23, 162, 184, 0.15);
    transform: translateY(-1px);
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Cartes de validation en attente */
.pending-validation-card {
    border: 3px solid #17a2b8 !important;
    border-radius: 15px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.pending-validation-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #17a2b8, #20c997, #28a745);
    background-size: 200% 100%;
    animation: gradientShift 2s ease-in-out infinite;
}

.pending-validation-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(23, 162, 184, 0.2);
    border-color: #138496 !important;
}

/* Animation de pulsation pour attirer l'attention */
@keyframes pulse-border {
    0% { border-color: #17a2b8; }
    50% { border-color: #20c997; }
    100% { border-color: #17a2b8; }
}

.pending-validation-card {
    animation: pulse-border 3s ease-in-out infinite;
}

/* Cartes d'alerte avec contours colorés */
.alert-card-warning {
    border: 2px solid #ffc107 !important;
    border-radius: 12px;
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.alert-card-warning::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #ffc107, #fd7e14, #dc3545);
    background-size: 200% 100%;
    animation: gradientShift 2.5s ease-in-out infinite;
}

.alert-card-warning:hover {
    transform: translateY(-2px);
    border-color: #e0a800 !important;
    box-shadow: 0 6px 20px rgba(255, 193, 7, 0.3);
}

.alert-card-info {
    border: 2px solid #17a2b8 !important;
    border-radius: 12px;
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.alert-card-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #17a2b8, #20c997, #007bff);
    background-size: 200% 100%;
    animation: gradientShift 3s ease-in-out infinite;
}

.alert-card-info:hover {
    transform: translateY(-2px);
    border-color: #138496 !important;
    box-shadow: 0 6px 20px rgba(23, 162, 184, 0.3);
}

.modern-table {
    border-collapse: separate;
    border-spacing: 0;
}

.modern-table thead th {
    background: #f8fafc;
    font-weight: 600;
    font-size: 0.875rem;
    color: #4a5568;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 1rem 0.75rem;
}

.modern-table tbody tr {
    transition: all 0.2s ease;
}

.modern-table tbody tr:hover {
    background-color: #f7fafc;
    transform: scale(1.01);
    box-shadow: 0 4px 12px rgba(0,0,0,0.05);
}

.sale-id-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.875rem;
    border: 2px solid #5a67d8;
    box-shadow: 0 2px 6px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.sale-id-badge:hover {
    transform: translateY(-2px) scale(1.05);
    border-color: #4c51bf;
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.sale-id-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.sale-id-badge:hover::before {
    left: 100%;
}

.date-info {
    line-height: 1.3;
}

.product-info, .customer-info {
    line-height: 1.3;
}

.quantity-badge .badge {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
    border-radius: 12px;
    border: 2px solid #e2e8f0;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    color: #4a5568;
    font-weight: 600;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.quantity-badge .badge:hover {
    transform: translateY(-1px);
    border-color: #667eea;
    background: linear-gradient(135deg, #e2e8f0 0%, #f8fafc 100%);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.2);
}

.amount-info {
    line-height: 1.3;
}

.status-badge {
    padding: 0.5rem 0.75rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    border: 2px solid;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.status-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.status-pending {
    background: linear-gradient(135deg, #fff3cd 0%, #fef3cd 100%);
    color: #856404;
    border-color: #f0ad4e;
    box-shadow: 0 2px 4px rgba(240, 173, 78, 0.3);
}

.status-pending:hover {
    background: linear-gradient(135deg, #fef3cd 0%, #fff3cd 100%);
    border-color: #ec971f;
    box-shadow: 0 4px 8px rgba(240, 173, 78, 0.4);
}

.status-paid {
    background: linear-gradient(135deg, #d1edff 0%, #b8e6ff 100%);
    color: #0c5460;
    border-color: #17a2b8;
    box-shadow: 0 2px 4px rgba(23, 162, 184, 0.3);
}

.status-paid:hover {
    background: linear-gradient(135deg, #b8e6ff 0%, #d1edff 100%);
    border-color: #138496;
    box-shadow: 0 4px 8px rgba(23, 162, 184, 0.4);
}

.status-cancelled {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border-color: #dc3545;
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

.status-cancelled:hover {
    background: linear-gradient(135deg, #f5c6cb 0%, #f8d7da 100%);
    border-color: #c82333;
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.4);
}

.action-buttons .btn {
    border-radius: 8px;
    transition: all 0.2s ease;
}

.action-buttons .btn:hover {
    transform: translateY(-1px);
}

.sale-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    border: 2px solid #e2e8f0;
    position: relative;
}

.sale-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.sale-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
    border-color: #667eea;
}

.sale-card:hover::before {
    transform: scaleX(1);
}

/* Variantes de couleurs pour les cartes selon le statut */
.sale-card[data-status="paid"] {
    border-color: #28a745;
}

.sale-card[data-status="paid"]::before {
    background: linear-gradient(90deg, #28a745, #20c997);
}

.sale-card[data-status="paid"]:hover {
    border-color: #1e7e34;
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.2);
}

.sale-card[data-status="pending"] {
    border-color: #ffc107;
}

.sale-card[data-status="pending"]::before {
    background: linear-gradient(90deg, #ffc107, #fd7e14);
}

.sale-card[data-status="pending"]:hover {
    border-color: #e0a800;
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.2);
}

.sale-card[data-status="cancelled"] {
    border-color: #dc3545;
}

.sale-card[data-status="cancelled"]::before {
    background: linear-gradient(90deg, #dc3545, #e83e8c);
}

.sale-card[data-status="cancelled"]:hover {
    border-color: #c82333;
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.2);
}

.sale-card-header {
    background: #f8fafc;
    padding: 1rem;
    border-bottom: 1px solid #e2e8f0;
}

.sale-card-body {
    padding: 1rem;
}

.sale-card-footer {
    background: #f8fafc;
    padding: 1rem;
    border-top: 1px solid #e2e8f0;
}

.sale-id {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 15px;
    font-weight: 600;
    font-size: 0.8rem;
    border: 2px solid #5a67d8;
    box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
    display: inline-block;
    transition: all 0.3s ease;
}

.sale-id:hover {
    transform: translateY(-1px) scale(1.05);
    border-color: #4c51bf;
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.4);
}

.empty-state {
    padding: 3rem 1rem;
}

.bulk-actions-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.98);
    color: #2d3748;
    padding: 1rem 0;
    z-index: 1000;
    box-shadow: 0 -4px 12px rgba(0,0,0,0.15);
    border-top: 2px solid #e2e8f0;
    backdrop-filter: blur(10px);
}

.pagination-wrapper .pagination {
    margin: 0;
}

.pagination-wrapper .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: none;
    color: #4a5568;
}

.pagination-wrapper .page-item.active .page-link {
    background: #667eea;
    border-color: #667eea;
}

@media (max-width: 768px) {
    .stat-value {
        font-size: 1.25rem;
    }

    .modern-table {
        font-size: 0.875rem;
    }

    .sale-card {
        margin-bottom: 1rem;
    }
}

/* Style pour la modal d'annulation */
.swal-wide {
    width: 600px !important;
}

.swal2-html-container ul {
    text-align: left;
    padding-left: 1.5rem;
}

.swal2-html-container .alert {
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
}

.swal2-html-container .alert-warning {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

/* Styles pour la modale de filtres avancés */
.advanced-filter-modal .modal-content {
    border: none;
    border-radius: 20px;
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
    overflow: hidden;
}

.advanced-filter-modal .modal-header {
    border: none;
    border-radius: 20px 20px 0 0;
}

.advanced-filter-modal .modal-footer {
    border: none;
    border-radius: 0 0 20px 20px;
}

/* Header avec gradient */
.gradient-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1.5rem;
}

.modal-icon {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
}

/* Footer avec gradient */
.gradient-footer {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 1.5rem;
}

/* Sections de filtres */
.filter-section {
    background: #f8fafc;
    border-radius: 15px;
    padding: 1.5rem;
    border-left: 4px solid #667eea;
    transition: all 0.3s ease;
}

.filter-section:hover {
    background: #f1f5f9;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.section-icon {
    width: 35px;
    height: 35px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 0.75rem;
    font-size: 0.9rem;
}

.section-title {
    color: #2d3748;
    font-weight: 600;
    margin: 0;
    font-size: 1rem;
}

/* Amélioration des form-floating */
.advanced-filter-modal .form-floating > label {
    color: #4a5568;
    font-weight: 500;
}

.advanced-filter-modal .form-floating > .form-control:focus ~ label,
.advanced-filter-modal .form-floating > .form-control:not(:placeholder-shown) ~ label,
.advanced-filter-modal .form-floating > .form-select ~ label {
    color: #667eea;
    font-weight: 600;
}

.advanced-filter-modal .form-control:focus,
.advanced-filter-modal .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Boutons de période rapide */
.quick-period-buttons {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.quick-period-buttons .btn {
    border-radius: 20px;
    font-size: 0.875rem;
    padding: 0.375rem 1rem;
    transition: all 0.3s ease;
}

.quick-period-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
}

/* Slider de montant */
.amount-slider {
    background: white;
    border-radius: 10px;
    padding: 1rem;
    border: 1px solid #e2e8f0;
}

.range-slider {
    position: relative;
    margin: 1rem 0;
}

.advanced-filter-modal .form-range {
    width: 100%;
    height: 6px;
    background: linear-gradient(to right, #e2e8f0 0%, #667eea 50%, #764ba2 100%);
    border-radius: 3px;
    outline: none;
}

.advanced-filter-modal .form-range::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

.advanced-filter-modal .form-range::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

.range-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: #4a5568;
}

#rangeValue {
    color: #667eea;
    font-weight: 600;
}

/* Animation d'entrée pour la modale */
.modal.fade .modal-dialog {
    transform: scale(0.8) translateY(-50px);
    transition: all 0.3s ease;
}

.modal.show .modal-dialog {
    transform: scale(1) translateY(0);
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialisation des tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Variables pour les filtres
    const searchInput = document.getElementById('searchInput');
    const statusFilter = document.getElementById('statusFilter');
    const dateFilter = document.getElementById('dateFilter');
    const rows = document.querySelectorAll('.sale-row');

    // Gestion de la sélection multiple
    const selectAllCheckbox = document.getElementById('selectAll');
    const saleCheckboxes = document.querySelectorAll('.sale-checkbox');
    const bulkActionsBar = document.getElementById('bulkActionsBar');
    const selectedCountSpan = document.getElementById('selectedCount');

    // Fonction de filtrage principal
    function filterTable() {
        const searchValue = searchInput.value.toLowerCase();
        const statusValue = statusFilter.value;
        const dateValue = dateFilter.value;

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const status = row.querySelector('.status-badge')?.textContent.trim();
            const dateCell = row.querySelector('.date-info .fw-semibold')?.textContent;

            let matchesSearch = text.includes(searchValue);
            let matchesStatus = !statusValue || status.includes(statusValue);
            let matchesDate = true;

            // Filtrage par date
            if (dateValue && dateCell) {
                const rowDate = new Date(dateCell.split('/').reverse().join('-'));
                const today = new Date();

                switch(dateValue) {
                    case 'today':
                        matchesDate = rowDate.toDateString() === today.toDateString();
                        break;
                    case 'week':
                        const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
                        matchesDate = rowDate >= weekAgo;
                        break;
                    case 'month':
                        const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
                        matchesDate = rowDate >= monthAgo;
                        break;
                }
            }

            row.style.display = (matchesSearch && matchesStatus && matchesDate) ? '' : 'none';
        });

        updateVisibleCount();
    }

    // Mise à jour du compteur de résultats visibles
    function updateVisibleCount() {
        const visibleRows = Array.from(rows).filter(row => row.style.display !== 'none');
        const badge = document.querySelector('.card-header .badge');
        if (badge) {
            badge.textContent = `${visibleRows.length} ventes`;
        }
    }

    // Gestion de la sélection multiple
    function updateBulkActions() {
        const checkedBoxes = document.querySelectorAll('.sale-checkbox:checked');
        const count = checkedBoxes.length;

        selectedCountSpan.textContent = count;
        bulkActionsBar.style.display = count > 0 ? 'block' : 'none';

        // Mise à jour du checkbox "Tout sélectionner"
        if (count === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (count === saleCheckboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
        }
    }

    // Event listeners
    searchInput.addEventListener('input', filterTable);
    statusFilter.addEventListener('change', filterTable);
    dateFilter.addEventListener('change', filterTable);

    // Sélection multiple
    selectAllCheckbox.addEventListener('change', function() {
        saleCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActions();
    });

    saleCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });

    // Gestion du slider de montant
    const amountRange = document.getElementById('amountRange');
    const rangeValue = document.getElementById('rangeValue');

    if (amountRange && rangeValue) {
        amountRange.addEventListener('input', function() {
            const value = parseInt(this.value);
            rangeValue.textContent = new Intl.NumberFormat('fr-FR').format(value) + ' FCFA';
            document.getElementById('maxAmount').value = value;
        });
    }

    // Initialisation
    updateVisibleCount();
});

// Fonctions globales pour la modale de filtres
function setQuickPeriod(period) {
    const today = new Date();
    const startDate = document.getElementById('startDate');
    const endDate = document.getElementById('endDate');

    // Réinitialiser les boutons
    document.querySelectorAll('.quick-period-buttons .btn').forEach(btn => {
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-outline-primary');
    });

    // Activer le bouton cliqué
    event.target.classList.remove('btn-outline-primary');
    event.target.classList.add('btn-primary');

    let start, end;

    switch(period) {
        case 'today':
            start = end = today;
            break;
        case 'week':
            start = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            end = today;
            break;
        case 'month':
            start = new Date(today.getFullYear(), today.getMonth(), 1);
            end = today;
            break;
        case 'quarter':
            const quarter = Math.floor(today.getMonth() / 3);
            start = new Date(today.getFullYear(), quarter * 3, 1);
            end = today;
            break;
    }

    startDate.value = start.toISOString().split('T')[0];
    endDate.value = end.toISOString().split('T')[0];
}

function resetAdvancedFilters() {
    // Réinitialiser tous les champs
    document.getElementById('startDate').value = '';
    document.getElementById('endDate').value = '';
    document.getElementById('minAmount').value = '';
    document.getElementById('maxAmount').value = '';
    document.getElementById('productFilter').value = '';
    document.getElementById('statusFilterAdvanced').value = '';
    document.getElementById('customerName').value = '';
    document.getElementById('customerPhone').value = '';
    document.getElementById('amountRange').value = '0';
    document.getElementById('rangeValue').textContent = '0 FCFA';

    // Réinitialiser les boutons de période
    document.querySelectorAll('.quick-period-buttons .btn').forEach(btn => {
        btn.classList.remove('btn-primary');
        btn.classList.add('btn-outline-primary');
    });

    // Animation de confirmation
    Swal.fire({
        title: 'Filtres réinitialisés',
        text: 'Tous les filtres ont été remis à zéro.',
        icon: 'success',
        timer: 1500,
        showConfirmButton: false
    });
}

// Fonctions globales
function toggleView(viewType) {
    const tableView = document.getElementById('tableView');
    const gridView = document.getElementById('gridView');
    const tableBtn = document.getElementById('tableViewBtn');
    const gridBtn = document.getElementById('gridViewBtn');

    if (viewType === 'table') {
        tableView.classList.remove('d-none');
        gridView.classList.add('d-none');
        tableBtn.classList.add('active');
        gridBtn.classList.remove('active');
    } else {
        tableView.classList.add('d-none');
        gridView.classList.remove('d-none');
        tableBtn.classList.remove('active');
        gridBtn.classList.add('active');
    }
}

function resetFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('statusFilter').value = '';
    document.getElementById('dateFilter').value = '';

    // Réinitialiser les filtres avancés
    document.getElementById('startDate').value = '';
    document.getElementById('endDate').value = '';
    document.getElementById('minAmount').value = '';
    document.getElementById('maxAmount').value = '';
    document.getElementById('productFilter').value = '';

    // Réappliquer les filtres
    document.querySelectorAll('.sale-row').forEach(row => {
        row.style.display = '';
    });

    // Mettre à jour le compteur
    const badge = document.querySelector('.card-header .badge');
    if (badge) {
        badge.textContent = `<?php echo e($sales->total()); ?> ventes`;
    }
}

function applyAdvancedFilters() {
    // Récupérer toutes les valeurs des filtres
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const minAmount = document.getElementById('minAmount').value;
    const maxAmount = document.getElementById('maxAmount').value;
    const product = document.getElementById('productFilter').value;
    const status = document.getElementById('statusFilterAdvanced').value;
    const customerName = document.getElementById('customerName').value.toLowerCase();
    const customerPhone = document.getElementById('customerPhone').value;

    const rows = document.querySelectorAll('.sale-row');
    let filteredCount = 0;

    // Animation de chargement
    const loadingToast = Swal.fire({
        title: 'Application des filtres...',
        html: 'Veuillez patienter',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    // Simuler un délai pour l'animation
    setTimeout(() => {
        rows.forEach(row => {
            let show = true;

            // Filtrage par date
            if (startDate || endDate) {
                const dateCell = row.querySelector('.date-info .fw-semibold')?.textContent;
                if (dateCell) {
                    const rowDate = new Date(dateCell.split('/').reverse().join('-'));
                    if (startDate && rowDate < new Date(startDate)) show = false;
                    if (endDate && rowDate > new Date(endDate)) show = false;
                }
            }

            // Filtrage par montant
            if (minAmount || maxAmount) {
                const amountText = row.querySelector('.amount-info .fw-bold')?.textContent;
                if (amountText) {
                    const amount = parseInt(amountText.replace(/[^\d]/g, ''));
                    if (minAmount && amount < parseInt(minAmount)) show = false;
                    if (maxAmount && amount > parseInt(maxAmount)) show = false;
                }
            }

            // Filtrage par produit
            if (product) {
                const productCell = row.querySelector('.product-info .fw-semibold')?.textContent;
                if (!productCell || !productCell.includes(product)) show = false;
            }

            // Filtrage par statut
            if (status) {
                const statusCell = row.querySelector('.status-badge')?.textContent.toLowerCase();
                const statusMap = {
                    'paid': 'payé',
                    'pending': 'en attente',
                    'cancelled': 'annulé'
                };
                if (!statusCell || !statusCell.includes(statusMap[status])) show = false;
            }

            // Filtrage par nom de client
            if (customerName) {
                const customerCell = row.querySelector('.customer-info .fw-semibold')?.textContent.toLowerCase();
                if (!customerCell || !customerCell.includes(customerName)) show = false;
            }

            // Filtrage par téléphone
            if (customerPhone) {
                const phoneCell = row.querySelector('.customer-info small')?.textContent;
                if (!phoneCell || !phoneCell.includes(customerPhone)) show = false;
            }

            // Appliquer l'affichage avec animation
            if (show) {
                row.style.display = '';
                row.style.animation = 'slideInUp 0.3s ease-out';
                filteredCount++;
            } else {
                row.style.display = 'none';
            }
        });

        // Fermer le loading et la modal
        loadingToast.close();

        const modal = bootstrap.Modal.getInstance(document.getElementById('filterModal'));
        modal.hide();

        // Mettre à jour le compteur avec animation
        const badge = document.querySelector('.card-header .badge');
        if (badge) {
            badge.style.animation = 'pulse 0.5s ease-in-out';
            badge.textContent = `${filteredCount} ventes`;
        }

        // Afficher un message de confirmation
        const totalFilters = [startDate, endDate, minAmount, maxAmount, product, status, customerName, customerPhone]
            .filter(filter => filter && filter.trim() !== '').length;

        if (totalFilters > 0) {
            Swal.fire({
                title: 'Filtres appliqués !',
                html: `<strong>${filteredCount}</strong> vente(s) trouvée(s)<br>avec <strong>${totalFilters}</strong> filtre(s) actif(s)`,
                icon: 'success',
                timer: 2000,
                showConfirmButton: false,
                position: 'top-end',
                toast: true
            });
        }

    }, 500); // Délai pour l'animation
}

function deleteSale(saleId) {
    Swal.fire({
        title: 'Confirmer la suppression',
        text: 'Êtes-vous sûr de vouloir supprimer cette vente ?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Oui, supprimer',
        cancelButtonText: 'Annuler'
    }).then((result) => {
        if (result.isConfirmed) {
            // Créer et soumettre le formulaire de suppression
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = `/admin/sales/${saleId}`;
            form.style.display = 'none';

            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            form.appendChild(csrfToken);

            const methodInput = document.createElement('input');
            methodInput.type = 'hidden';
            methodInput.name = '_method';
            methodInput.value = 'DELETE';
            form.appendChild(methodInput);

            document.body.appendChild(form);
            form.submit();
        }
    });
}

function printInvoice(saleId) {
    window.open(`/admin/sales/${saleId}/invoice`, '_blank');
}

function exportSales() {
    // Créer un formulaire pour l'export
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '/admin/sales/export';
    form.style.display = 'none';

    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    form.appendChild(csrfToken);

    document.body.appendChild(form);
    form.submit();
}

function exportSelected() {
    const selectedIds = Array.from(document.querySelectorAll('.sale-checkbox:checked'))
        .map(checkbox => checkbox.value);

    if (selectedIds.length === 0) {
        Swal.fire('Aucune sélection', 'Veuillez sélectionner au moins une vente.', 'info');
        return;
    }

    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '/admin/sales/export-selected';
    form.style.display = 'none';

    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    form.appendChild(csrfToken);

    selectedIds.forEach(id => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'sale_ids[]';
        input.value = id;
        form.appendChild(input);
    });

    document.body.appendChild(form);
    form.submit();
}

function deleteSelected() {
    const selectedIds = Array.from(document.querySelectorAll('.sale-checkbox:checked'))
        .map(checkbox => checkbox.value);

    if (selectedIds.length === 0) {
        Swal.fire('Aucune sélection', 'Veuillez sélectionner au moins une vente.', 'info');
        return;
    }

    Swal.fire({
        title: 'Confirmer la suppression',
        text: `Êtes-vous sûr de vouloir supprimer ${selectedIds.length} vente(s) ?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Oui, supprimer',
        cancelButtonText: 'Annuler'
    }).then((result) => {
        if (result.isConfirmed) {
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '/admin/sales/delete-selected';
            form.style.display = 'none';

            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = '_token';
            csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
            form.appendChild(csrfToken);

            selectedIds.forEach(id => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'sale_ids[]';
                input.value = id;
                form.appendChild(input);
            });

            document.body.appendChild(form);
            form.submit();
        }
    });
}
</script>
<?php $__env->stopPush(); ?>

<!-- Modal pour modifier la date de vente -->
<div class="modal fade" id="editSaleDateModal" tabindex="-1" aria-labelledby="editSaleDateModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="editSaleDateModalLabel">
                    <i class="fas fa-calendar-edit me-2"></i>Modifier la date de vente
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editSaleDateForm" method="POST">
                <?php echo csrf_field(); ?>
                <?php echo method_field('PATCH'); ?>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sale_date" class="form-label">Date de vente</label>
                                <input type="date" class="form-control" id="sale_date" name="sale_date" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sale_time" class="form-label">Heure de vente</label>
                                <input type="time" class="form-control" id="sale_time" name="sale_time" required>
                            </div>
                        </div>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Attention :</strong> La modification de la date de vente peut affecter les rapports et statistiques.
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save me-1"></i>Modifier la date
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
function editSaleDate(saleId, currentDate, currentTime) {
    // Remplir le formulaire avec les données actuelles
    document.getElementById('sale_date').value = currentDate;
    document.getElementById('sale_time').value = currentTime;

    // Définir l'action du formulaire
    const form = document.getElementById('editSaleDateForm');
    form.action = `/admin/sales/${saleId}/update-date`;

    // Afficher le modal
    const modal = new bootstrap.Modal(document.getElementById('editSaleDateModal'));
    modal.show();
}

// Gérer la soumission du formulaire
document.getElementById('editSaleDateForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const actionUrl = this.action;

    // Afficher un loader
    Swal.fire({
        title: 'Modification en cours...',
        allowOutsideClick: false,
        didOpen: () => {
            Swal.showLoading();
        }
    });

    fetch(actionUrl, {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            Swal.fire({
                icon: 'success',
                title: 'Succès',
                text: data.message,
                timer: 2000,
                showConfirmButton: false
            }).then(() => {
                // Fermer le modal et recharger la page
                bootstrap.Modal.getInstance(document.getElementById('editSaleDateModal')).hide();
                location.reload();
            });
        } else {
            Swal.fire({
                icon: 'error',
                title: 'Erreur',
                text: data.message || 'Une erreur est survenue'
            });
        }
    })
    .catch(error => {
        console.error('Erreur:', error);
        Swal.fire({
            icon: 'error',
            title: 'Erreur',
            text: 'Une erreur est survenue lors de la modification'
        });
    });
});

// Fonction pour annuler une vente
function cancelSale(saleId, customerName, quantity) {
    Swal.fire({
        title: 'Confirmer l\'annulation',
        html: `
            <div class="text-start">
                <p><strong>Êtes-vous sûr de vouloir annuler cette vente ?</strong></p>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Attention :</strong> Cette action va :
                    <ul class="mt-2 mb-0">
                        <li>Marquer la vente comme annulée</li>
                        <li>Remettre la quantité de <strong>${quantity} tonnes</strong> sur le camion</li>
                        <li>Remettre la quantité dans le stock général</li>
                    </ul>
                </div>
                <p class="mb-0"><strong>Client :</strong> ${customerName}</p>
                <p class="mb-0"><strong>Quantité :</strong> ${quantity} tonnes</p>
            </div>
        `,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Oui, annuler la vente',
        cancelButtonText: 'Non, garder la vente',
        reverseButtons: true,
        customClass: {
            popup: 'swal-wide'
        }
    }).then((result) => {
        if (result.isConfirmed) {
            // Afficher un loader
            Swal.fire({
                title: 'Annulation en cours...',
                text: 'Veuillez patienter pendant l\'annulation de la vente.',
                allowOutsideClick: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Envoyer la requête d'annulation
            fetch(`/admin/sales/${saleId}/cancel`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    Swal.fire({
                        icon: 'success',
                        title: 'Vente annulée !',
                        text: data.message,
                        timer: 3000,
                        showConfirmButton: false
                    }).then(() => {
                        // Recharger la page pour voir les changements
                        location.reload();
                    });
                } else {
                    Swal.fire({
                        icon: 'error',
                        title: 'Erreur',
                        text: data.message || 'Une erreur est survenue lors de l\'annulation'
                    });
                }
            })
            .catch(error => {
                console.error('Erreur:', error);
                Swal.fire({
                    icon: 'error',
                    title: 'Erreur',
                    text: 'Une erreur est survenue lors de l\'annulation de la vente'
                });
            });
        }
    });
}
</script>

<?php echo $__env->make('layouts.admin_minimal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/admin/sales/index.blade.php ENDPATH**/ ?>
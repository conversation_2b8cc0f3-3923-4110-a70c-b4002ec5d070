@extends('layouts.accountant')

@section('title', 'Gestion des Recouvrements')

@push('styles')
<style>
    /* Style pour ajouter de l'espace en haut du contenu principal */
    #content {
        padding-top: 20px;
        scroll-margin-top: 80px; /* Marge pour le scroll lors de la navigation avec ancre */
        scroll-behavior: smooth;
    }
    
    /* Marge importante pour éviter le chevauchement avec le menu horizontal */
    .container-fluid {
        margin-top: 70px !important; /* Valeur importante pour éviter les conflits de style */
    }
    
    /* Style spécifique pour la pagination */
    .pagination-container {
        margin-top: 30px;
        padding-top: 20px;
    }
    /* Style général de la page */
    .recovery-dashboard {
        background-color: #f8f9fa;
        border-radius: 10px;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.05);
        padding: 20px;
        margin-bottom: 30px;
    }
    
    /* Cartes de statistiques */
    .stat-card {
        background: linear-gradient(135deg, #4e73df 0%, #224abe 100%);
        color: white;
        border-radius: 10px;
        padding: 20px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        height: 100%;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 6px 15px rgba(0, 0, 0, 0.15);
    }
    
    .stat-card i {
        font-size: 2.5rem;
        margin-bottom: 15px;
        opacity: 0.8;
    }
    
    .stat-card .stat-value {
        font-size: 1.8rem;
        font-weight: 700;
        margin-bottom: 5px;
    }
    
    .stat-card .stat-label {
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 1px;
        opacity: 0.8;
    }
    
    /* Tableau des recouvrements */
    .recovery-table {
        background-color: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
        font-size: 0.9rem;
    }
    
    .recovery-table .table {
        margin-bottom: 0;
    }
    
    .recovery-table thead {
        background-color: #4e73df;
        color: white;
    }
    
    .recovery-table th {
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.5px;
        padding: 10px 12px;
        border: none;
    }
    
    .recovery-table td {
        padding: 8px 12px;
        vertical-align: middle;
        border-color: #f0f0f0;
    }
    
    /* Badges et étiquettes */
    .badge {
        padding: 4px 8px;
        font-weight: 600;
        font-size: 0.7rem;
        border-radius: 30px;
    }
    
    .badge-overdue {
        background-color: #e74a3b;
        color: white;
    }
    
    .badge-status {
        padding: 4px 8px;
        font-weight: 500;
        border-radius: 4px;
        font-size: 0.75rem;
        display: inline-flex;
        align-items: center;
    }
    
    /* Pagination */
    .pagination {
        margin-top: 20px;
    }
    
    .page-item.active .page-link {
        background-color: #4e73df;
        border-color: #4e73df;
    }
    
    .page-link {
        color: #4e73df;
        padding: 10px 15px;
        border-radius: 5px;
        margin: 0 3px;
    }
    
    /* Filtres */
    .filter-section {
        background-color: white;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
    }
    
    .filter-section .form-control {
        border-radius: 5px;
        padding: 10px 15px;
        border: 1px solid #e0e0e0;
    }
    
    .filter-section .btn-filter {
        background-color: #4e73df;
        color: white;
        border-radius: 5px;
        padding: 10px 20px;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .filter-section .btn-filter:hover {
        background-color: #2e59d9;
        transform: translateY(-2px);
    }
    
    /* Styles pour les alertes clignotantes avec couleurs plus vives */
    tr.yellow-alert {
        background-color: rgba(255, 235, 59, 0.25) !important;
        border-left: 5px solid #ffeb3b !important;
    }
    
    tr.orange-alert {
        background-color: rgba(255, 152, 0, 0.25) !important;
        border-left: 5px solid #ff9800 !important;
    }
    
    tr.red-alert {
        background-color: rgba(244, 67, 54, 0.25) !important;
        border-left: 5px solid #f44336 !important;
    }
    
    /* Animation clignotante améliorée avec plus de contraste */
    @keyframes blink {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.4; }
    }
    
    tr.yellow-alert {
        animation: blink 1.2s infinite !important;
    }
    
    tr.orange-alert {
        animation: blink 0.9s infinite !important;
    }
    
    tr.red-alert {
        animation: blink 0.7s infinite !important;
    }
    
    tr.yellow-alert:hover,
    tr.orange-alert:hover,
    tr.red-alert:hover {
        animation: none !important;
        opacity: 1 !important;
    }
    
    /* Icônes et indicateurs */
    .status-icon {
        font-size: 1.2rem;
        margin-right: 5px;
    }
    
    .paid-status {
        color: #1cc88a;
    }
    
    .pending-status {
        color: #f6c23e;
    }
    
    .unpaid-status {
        color: #e74a3b;
    }
    
    /* Animation pour l'effet clignotant avec des couleurs plus visibles */
    @keyframes blink-yellow {
        0%, 100% { background-color: rgba(253, 224, 71, 0.4); }
        50% { background-color: rgba(253, 224, 71, 0.7); }
    }
    
    @keyframes blink-orange {
        0%, 100% { background-color: rgba(249, 115, 22, 0.4); }
        50% { background-color: rgba(249, 115, 22, 0.7); }
    }
    
    @keyframes blink-red {
        0%, 100% { background-color: rgba(220, 38, 38, 0.4); }
        50% { background-color: rgba(220, 38, 38, 0.7); }
    }
    
    /* Styles pour les lignes en retard avec des couleurs plus visibles */
    tr.overdue-notice {
        background-color: rgba(253, 224, 71, 0.4) !important;
        animation: blink-yellow 1.5s infinite !important;
    }
    
    tr.overdue-notice:hover {
        background-color: rgba(253, 224, 71, 0.6) !important;
        animation: none !important;
    }
    
    tr.overdue-warning {
        background-color: rgba(249, 115, 22, 0.4) !important;
        animation: blink-orange 1.5s infinite !important;
    }
    
    tr.overdue-warning:hover {
        background-color: rgba(249, 115, 22, 0.6) !important;
        animation: none !important;
    }
    
    tr.overdue-critical {
        background-color: rgba(220, 38, 38, 0.4) !important;
        animation: blink-red 1.5s infinite !important;
    }
    
    tr.overdue-critical:hover {
        background-color: rgba(220, 38, 38, 0.6) !important;
        animation: none !important;
    }
    
    /* Badge pour indiquer l'ancienneté */
    .overdue-badge {
        display: inline-block;
        padding: 0.2rem 0.5rem;
        border-radius: 4px;
        font-size: 0.7rem;
        font-weight: 600;
        margin-left: 0.5rem;
    }
    
    .overdue-badge.notice {
        background-color: rgba(253, 224, 71, 0.2);
        color: #854d0e;
        border: 1px solid rgba(253, 224, 71, 0.4);
    }
    
    .overdue-badge.warning {
        background-color: rgba(249, 115, 22, 0.2);
        color: #9a3412;
        border: 1px solid rgba(249, 115, 22, 0.4);
    }
    
    .overdue-badge.critical {
        background-color: rgba(220, 38, 38, 0.2);
        color: #b91c1c;
        border: 1px solid rgba(220, 38, 38, 0.4);
    }
    
    .card {
        border: none;
        border-radius: 10px;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.05);
        margin-bottom: 24px;
        transition: transform 0.2s, box-shadow 0.2s;
    }
    
    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.1);
    }
    
    .card-header {
        background-color: #fff;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        padding: 1.25rem 1.5rem;
        border-radius: 10px 10px 0 0;
    }
    
    .card-header h5 {
        margin-bottom: 0;
        font-weight: 600;
    }
    
    .stats-card {
        padding: 1.5rem;
        border-radius: 10px;
        background: #fff;
        height: 100%;
    }
    
    .stats-card .stats-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 1rem;
    }
    
    .stats-card .stats-icon.paid {
        background-color: rgba(22, 163, 74, 0.1);
        color: #16a34a;
    }
    
    .stats-card .stats-icon.partial {
        background-color: rgba(202, 138, 4, 0.1);
        color: #ca8a04;
    }
    
    .stats-card .stats-icon.unpaid {
        background-color: rgba(220, 38, 38, 0.1);
        color: #dc2626;
    }
    
    .stats-card .stats-value {
        font-size: 1.75rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }
    
    .stats-card .stats-label {
        color: #64748b;
        font-size: 0.875rem;
    }
    
    .table {
        width: 100%;
        margin-bottom: 0;
    }
    
    .table th {
        font-weight: 600;
        color: #1e293b;
        border-bottom: 2px solid #e2e8f0;
        padding: 0.75rem 1.5rem;
    }
    
    .table td {
        padding: 1rem 1.5rem;
        vertical-align: middle;
        color: #334155;
        border-bottom: 1px solid #f1f5f9;
    }
    
    .table tr:hover {
        background-color: #f8fafc;
    }
    
    .badge {
        padding: 0.35em 0.65em;
        font-size: 0.75em;
        font-weight: 600;
        border-radius: 0.25rem;
    }
    
    .badge-paid {
        background-color: rgba(22, 163, 74, 0.1);
        color: #16a34a;
    }
    
    .badge-partial {
        background-color: rgba(202, 138, 4, 0.1);
        color: #ca8a04;
    }
    
    .badge-unpaid {
        background-color: rgba(220, 38, 38, 0.1);
        color: #dc2626;
    }
    
    .progress {
        height: 8px;
        border-radius: 4px;
        background-color: #e2e8f0;
        margin-top: 0.5rem;
    }
    
    .progress-bar {
        border-radius: 4px;
    }
    
    .progress-paid {
        background-color: #16a34a;
    }
    
    .progress-partial {
        background-color: #ca8a04;
    }
    
    .progress-unpaid {
        background-color: #dc2626;
    }
    
    .action-btn {
        width: 32px;
        height: 32px;
        padding: 0;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border-radius: 6px;
        background-color: #f1f5f9;
        color: #64748b;
        margin-right: 0.25rem;
        transition: all 0.2s;
    }
    
    .action-btn:hover {
        background-color: #e2e8f0;
        color: #334155;
    }
    
    .action-btn.view {
        background-color: rgba(37, 99, 235, 0.1);
        color: #2563eb;
    }
    
    .action-btn.view:hover {
        background-color: rgba(37, 99, 235, 0.2);
    }
    
    .action-btn.edit {
        background-color: rgba(202, 138, 4, 0.1);
        color: #ca8a04;
    }
    
    .action-btn.edit:hover {
        background-color: rgba(202, 138, 4, 0.2);
    }
    
    .action-btn.delete {
        background-color: rgba(220, 38, 38, 0.1);
        color: #dc2626;
    }
    
    .action-btn.delete:hover {
        background-color: rgba(220, 38, 38, 0.2);
    }
    
    .search-box {
        position: relative;
    }
    
    .search-box .search-icon {
        position: absolute;
        left: 1rem;
        top: 50%;
        transform: translateY(-50%);
        color: #64748b;
    }
    
    .search-box input {
        padding-left: 2.5rem;
        border-radius: 0.5rem;
        border: 1px solid #e2e8f0;
    }
    
    .filters-section {
        background-color: #f8fafc;
        border-radius: 0.5rem;
        padding: 1rem;
        margin-bottom: 1.5rem;
    }
</style>
@endpush

@section('content')
<div class="container-fluid mt-4">
    <div class="d-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Gestion des Recouvrements</h1>
    </div>
    
    <!-- Espacement supplémentaire -->
    <div class="mb-3"></div>

    <!-- Statistiques -->
    <div class="row mb-4">
        <div class="col-md-4 mb-4 mb-md-0">
            <div class="stats-card">
                <div class="stats-icon paid">
                    <i class="fas fa-check-circle"></i>
                </div>
                <div class="stats-value">{{ $sales->where('payment_status', 'paid')->count() }}</div>
                <div class="stats-label">Ventes Payées</div>
            </div>
        </div>
        <div class="col-md-4 mb-4 mb-md-0">
            <div class="stats-card">
                <div class="stats-icon partial">
                    <i class="fas fa-clock"></i>
                </div>
                <div class="stats-value">{{ $sales->where('payment_status', 'partial')->count() }}</div>
                <div class="stats-label">Paiements Partiels</div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="stats-card">
                <div class="stats-icon unpaid">
                    <i class="fas fa-exclamation-circle"></i>
                </div>
                <div class="stats-value">{{ $sales->where('payment_status', 'unpaid')->count() }}</div>
                <div class="stats-label">Ventes Non Payées</div>
            </div>
        </div>
    </div>

    <!-- Filtres -->
    <!-- Légende pour le système de coloration -->
    <div class="card mb-4">
        <div class="card-header">
            <h5>Légende des Alertes de Recouvrement</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-4">
                    <div class="d-flex align-items-center mb-2">
                        <div style="width: 24px; height: 24px; background-color: rgba(253, 224, 71, 0.7); margin-right: 10px; border-radius: 4px; animation: blink-yellow 1.5s infinite;"></div>
                        <span><strong>Ventes non payées de plus de 15 jours</strong></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-flex align-items-center mb-2">
                        <div style="width: 24px; height: 24px; background-color: rgba(249, 115, 22, 0.7); margin-right: 10px; border-radius: 4px; animation: blink-orange 1.5s infinite;"></div>
                        <span><strong>Ventes non payées de plus de 30 jours</strong></span>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="d-flex align-items-center mb-2">
                        <div style="width: 24px; height: 24px; background-color: rgba(220, 38, 38, 0.7); margin-right: 10px; border-radius: 4px; animation: blink-red 1.5s infinite;"></div>
                        <span><strong>Ventes non payées de plus de 45 jours</strong></span>
                    </div>
                </div>
            </div>
            <div class="alert alert-info mt-2 mb-0">
                <i class="fas fa-info-circle me-2"></i> Les lignes clignotantes indiquent les ventes qui nécessitent une attention particulière pour le recouvrement.
            </div>
        </div>
    </div>

    <div class="card mb-4">
        <div class="card-header">
            <h5>Filtres et Recherche</h5>
        </div>
        <div class="card-body">
            <form action="{{ route('accountant.recoveries.index') }}" method="GET" class="row g-3">
                <div class="col-md-4">
                    <div class="search-box">
                        <span class="search-icon">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" class="form-control" name="search" placeholder="Rechercher..." value="{{ request('search') }}">
                    </div>
                </div>
                <div class="col-md-3">
                    <select class="form-select" name="status">
                        <option value="">Tous les statuts</option>
                        <option value="paid" {{ request('status') == 'paid' ? 'selected' : '' }}>Payé</option>
                        <option value="partial" {{ request('status') == 'partial' ? 'selected' : '' }}>Partiellement payé</option>
                        <option value="unpaid" {{ request('status') == 'unpaid' ? 'selected' : '' }}>Non payé</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select class="form-select" name="sort">
                        <option value="date_desc" {{ request('sort') == 'date_desc' ? 'selected' : '' }}>Date (récent d'abord)</option>
                        <option value="date_asc" {{ request('sort') == 'date_asc' ? 'selected' : '' }}>Date (ancien d'abord)</option>
                        <option value="amount_desc" {{ request('sort') == 'amount_desc' ? 'selected' : '' }}>Montant (élevé d'abord)</option>
                        <option value="amount_asc" {{ request('sort') == 'amount_asc' ? 'selected' : '' }}>Montant (faible d'abord)</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-filter me-2"></i> Filtrer
                    </button>
        </div>
    </div>
    
    <!-- Statistiques -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="stat-card">
                <i class="fas fa-chart-line"></i>
                <div class="stat-value">{{ $totalSales }}</div>
                <div class="stat-label">Total des ventes</div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stat-card" style="background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);">
                <i class="fas fa-check-circle"></i>
                <div class="stat-value">{{ $paidSales }}</div>
                <div class="stat-label">Ventes payées</div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stat-card" style="background: linear-gradient(135deg, #f6c23e 0%, #dda20a 100%);">
                <i class="fas fa-spinner"></i>
                <div class="stat-value">{{ $partialSales }}</div>
                <div class="stat-label">Paiements partiels</div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="stat-card" style="background: linear-gradient(135deg, #e74a3b 0%, #be2617 100%);">
                <i class="fas fa-exclamation-circle"></i>
                <div class="stat-value">{{ $unpaidSales }}</div>
                <div class="stat-label">Ventes impayées</div>
            </div>
        </div>
    </div>
    
    <!-- Tableau des recouvrements -->
    <div class="row">
        <div class="col-12">
            <div class="recovery-table">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th><i class="fas fa-file-invoice me-2"></i>Facture</th>
                            <th><i class="fas fa-user me-2"></i>Client</th>
                            <th><i class="fas fa-calendar-alt me-2"></i>Date</th>
                            <th><i class="fas fa-money-bill-wave me-2"></i>Montant</th>
                            <th><i class="fas fa-check-circle me-2"></i>Payé</th>
                            <th><i class="fas fa-balance-scale me-2"></i>Restant</th>
                            <th><i class="fas fa-info-circle me-2"></i>Statut</th>
                            <th><i class="fas fa-cogs me-2"></i>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($sales as $sale)
                            @php
                                $alertClass = '';
                                $badgeClass = '';
                                $daysLabel = '';
                                
                                if($sale->payment_status != 'paid') {
                                    if($sale->days_since_creation >= 45) {
                                        // Rouge avec effet clignotant
                                        $alertClass = 'red-alert';
                                        $badgeClass = 'badge bg-danger';
                                        $daysLabel = '+45 jours';
                                    } elseif($sale->days_since_creation >= 30) {
                                        // Orange avec effet clignotant
                                        $alertClass = 'orange-alert';
                                        $badgeClass = 'badge bg-warning text-dark';
                                        $daysLabel = '+30 jours';
                                    } elseif($sale->days_since_creation >= 15) {
                                        // Jaune avec effet clignotant
                                        $alertClass = 'yellow-alert';
                                        $badgeClass = 'badge bg-warning text-dark';
                                        $daysLabel = '+15 jours';
                                    }
                                }
                            @endphp
                            <tr class="{{ $alertClass }}" @if($sale->status == 'cancelled') style="opacity: 0.6; background-color: #f8f9fa; border-left: 4px solid #dc3545;" @endif>
                                <td>
                                    <span class="fw-bold" style="font-size: 0.85rem;">{{ $sale->invoice_number }}</span>
                                    @if($sale->status == 'cancelled')
                                        <span class="badge bg-danger ms-1">
                                            <i class="fas fa-ban me-1"></i>Annulée par l'admin
                                        </span>
                                    @elseif(!empty($daysLabel))
                                        <span class="badge badge-overdue ms-1">{{ $daysLabel }}</span>
                                    @endif
                                </td>
                                <td>
                                    <div class="d-flex flex-column">
                                        <span class="fw-semibold" style="font-size: 0.85rem;">{{ $sale->customer_name }}</span>
                                        <small class="text-muted" style="font-size: 0.75rem;">{{ $sale->customer_phone }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex flex-column">
                                        <span style="font-size: 0.85rem;">{{ $sale->created_at->format('d/m/Y') }}</span>
                                        <small class="text-muted" style="font-size: 0.75rem;">{{ $sale->days_since_creation }} j</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="fw-bold" style="font-size: 0.85rem;">{{ number_format($sale->total_amount, 0, ',', ' ') }}</span>
                                </td>
                                <td>
                                    <span class="fw-semibold {{ $sale->payment_status == 'paid' ? 'text-success' : '' }}" style="font-size: 0.85rem;">{{ number_format($sale->amount_paid, 0, ',', ' ') }}</span>
                                </td>
                                <td>
                                    <span class="fw-semibold {{ ($sale->total_amount - $sale->amount_paid) > 0 ? 'text-danger' : 'text-success' }}" style="font-size: 0.85rem;">{{ number_format($sale->total_amount - $sale->amount_paid, 0, ',', ' ') }}</span>
                                </td>
                                <td>
                                    <div class="d-flex flex-column" style="min-width: 100px;">
                                        @if($sale->payment_status == 'paid')
                                            <span class="badge-status bg-success-subtle text-success"><i class="fas fa-check-circle status-icon paid-status"></i> Payé</span>
                                        @elseif($sale->payment_status == 'partial')
                                            <span class="badge-status bg-warning-subtle text-warning"><i class="fas fa-clock status-icon pending-status"></i> Partiel</span>
                                        @else
                                            <span class="badge-status bg-danger-subtle text-danger"><i class="fas fa-times-circle status-icon unpaid-status"></i> Impayé</span>
                                        @endif
                                        
                                        <div class="progress mt-1" style="height: 4px;">
                                            @php
                                                $percentage = ($sale->amount_paid / $sale->total_amount) * 100;
                                            @endphp
                                            <div class="progress-bar {{ $percentage == 100 ? 'bg-success' : ($percentage > 0 ? 'bg-warning' : 'bg-danger') }}" 
                                                role="progressbar" 
                                                style="width: {{ $percentage }}%" 
                                                aria-valuenow="{{ $percentage }}" 
                                                aria-valuemin="0" 
                                                aria-valuemax="100">
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex gap-1">
                                        @if($sale->status == 'cancelled')
                                            <span class="text-muted" title="Vente annulée par l'administrateur">
                                                <i class="fas fa-ban"></i> Annulée
                                            </span>
                                            @if($sale->admin_note)
                                                <small class="text-muted d-block mt-1" style="font-size: 0.7rem;">{{ $sale->admin_note }}</small>
                                            @endif
                                        @else
                                            <a href="{{ route('accountant.recoveries.show', $sale->id) }}" class="btn btn-sm btn-primary p-1" style="width: 28px; height: 28px;" data-bs-toggle="tooltip" title="Détails"><i class="fas fa-eye"></i></a>
                                            <a href="{{ route('accountant.recoveries.edit', $sale->id) }}" class="btn btn-sm btn-success p-1" style="width: 28px; height: 28px;" data-bs-toggle="tooltip" title="Paiement"><i class="fas fa-money-bill-wave"></i></a>
                                            <button type="button" class="btn btn-sm btn-info text-white p-1 contact-btn" style="width: 28px; height: 28px;" data-bs-toggle="tooltip" title="Contacter" data-phone="{{ $sale->customer_phone }}" data-customer="{{ $sale->customer_name }}"><i class="fas fa-phone"></i></button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="text-center py-5">
                                    <div class="d-flex flex-column align-items-center">
                                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                        <h5 class="text-muted">Aucune vente trouvée</h5>
                                        <p class="text-muted">Essayez de modifier vos filtres de recherche</p>
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination améliorée -->
            <div id="pagination-section" class="pagination-container d-flex justify-content-between align-items-center px-3 pb-3">
                <div class="text-muted">
                    Affichage de <span class="fw-semibold">{{ $sales->firstItem() ?? 0 }}</span> à <span class="fw-semibold">{{ $sales->lastItem() ?? 0 }}</span> sur <span class="fw-semibold">{{ $sales->total() }}</span> ventes
                </div>
                <div>
                    {{ $sales->onEachSide(1)->links() }}
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialiser les tooltips Bootstrap
        const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        tooltips.forEach(tooltip => {
            new bootstrap.Tooltip(tooltip);
        });
        
        // Gestion des boutons de contact
        const contactButtons = document.querySelectorAll('.contact-btn');
        contactButtons.forEach(button => {
            button.addEventListener('click', function() {
                const phone = this.getAttribute('data-phone');
                const customer = this.getAttribute('data-customer');
                
                Swal.fire({
                    title: 'Contacter ' + customer,
                    html: `<p class="mb-3">Comment souhaitez-vous contacter ce client ?</p>`,
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#4e73df',
                    cancelButtonColor: '#1cc88a',
                    confirmButtonText: '<i class="fas fa-phone me-2"></i>Appeler',
                    cancelButtonText: '<i class="fab fa-whatsapp me-2"></i>WhatsApp',
                    reverseButtons: true,
                    showCloseButton: true,
                    allowOutsideClick: true,
                    customClass: {
                        container: 'contact-alert-container',
                        popup: 'contact-alert-popup',
                        title: 'contact-alert-title',
                        confirmButton: 'btn btn-primary btn-lg px-4 py-2',
                        cancelButton: 'btn btn-success btn-lg px-4 py-2',
                        actions: 'gap-2 mt-4'
                    },
                    buttonsStyling: false
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Appeler directement
                        window.location.href = 'tel:' + phone;
                    } else if (result.dismiss === Swal.DismissReason.cancel) {
                        // Contacter via WhatsApp
                        window.open('https://wa.me/' + phone.replace(/\D/g, ''), '_blank');
                    }
                });
            });
        });
        
        // Script pour gérer le scroll lors de la pagination
        // Vérifier si nous sommes sur une page paginée (page > 1)
        const urlParams = new URLSearchParams(window.location.search);
        const pageParam = urlParams.get('page');
        
        // Ajouter un hash à l'URL si nous sommes sur une page > 1 et qu'il n'y a pas déjà de hash
        if (pageParam && pageParam > 1 && !window.location.hash) {
            // Ajouter le hash #pagination-section à l'URL sans recharger la page
            history.replaceState(null, null, window.location.pathname + window.location.search + '#pagination-section');
        }
        
        // Si nous avons un hash dans l'URL, faire défiler jusqu'à cette section
        if (window.location.hash) {
            // Petit délai pour s'assurer que tout est chargé
            setTimeout(function() {
                // Récupérer l'élément cible
                const target = document.querySelector(window.location.hash);
                if (target) {
                    target.scrollIntoView({ behavior: 'smooth' });
                }
            }, 300);
        }
    });
</script>

<style>
    /* Styles personnalisés pour SweetAlert2 */
    .contact-alert-popup {
        border-radius: 15px;
        padding: 2rem;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    }
    
    .contact-alert-title {
        font-size: 1.5rem;
        font-weight: 600;
        color: #4e73df;
    }
    
    .swal2-html-container {
        font-size: 1.1rem;
        color: #5a5c69;
    }
    
    .swal2-icon {
        border-color: #4e73df !important;
        color: #4e73df !important;
    }
</style>
@endpush

<?php
    use Illuminate\Support\Str;
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">
    <meta name="app-url" content="<?php echo e(url('/')); ?>">
    <title><?php echo $__env->yieldContent('title'); ?> - <?php echo e(config('app.name', 'Gradis')); ?></title>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo e(asset('favicon.ico')); ?>">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- SweetAlert2 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/sweetalert2@11/dist/sweetalert2.min.css" rel="stylesheet">
    
    <?php echo $__env->yieldPushContent('styles'); ?>

    <!-- Custom CSS -->
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #475569;
            --success-color: #16a34a;
            --warning-color: #ca8a04;
            --danger-color: #dc2626;
            --light-color: #f1f5f9;
            --dark-color: #0f172a;
        }
        
        /* Wrapper pour le contenu principal */
        body {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            padding-bottom: 60px;
            background-color: #f8fafc;
            font-family: 'Poppins', sans-serif;
        }

        #wrapper {
            display: flex;
            flex: 1;
        }

        /* Sidebar moderne */
        .sidebar {
            position: fixed;
            top: 0;
            left: 0;
            width: 280px;
            height: 100vh;
            background: linear-gradient(180deg, #1E293B 0%, #0F172A 100%);
            overflow-y: auto;
            transition: all 0.3s ease;
            z-index: 1030;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
        }
        
        .sidebar-header {
            padding: 1.5rem;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            background-color: rgba(0, 0, 0, 0.2);
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            gap: 1rem;
        }
        
        .sidebar-logo {
            height: 40px;
            width: auto;
        }
        
        .sidebar-title {
            color: white;
            margin: 0;
            font-weight: 600;
            letter-spacing: 1px;
        }
        
        /* Profil utilisateur dans la sidebar */
        .sidebar-user {
            padding: 1.5rem;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .sidebar-avatar {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            font-size: 1.2rem;
            overflow: hidden;
        }
        
        .sidebar-avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .sidebar-user-info {
            flex: 1;
        }
        
        .sidebar-user-name {
            color: white;
            font-weight: 600;
            margin: 0;
            font-size: 0.95rem;
        }
        
        .sidebar-user-role {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.8rem;
            margin: 0;
        }

        .sidebar .nav-link {
            color: rgba(255, 255, 255, 0.8);
            padding: 0.75rem 1.5rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            text-decoration: none;
            border-left: 3px solid transparent;
            font-weight: 500;
        }

        .sidebar .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.05);
            color: white;
            border-left-color: rgba(255, 255, 255, 0.3);
        }

        .sidebar .nav-link.active {
            background-color: rgba(255, 255, 255, 0.1);
            color: white;
            border-left-color: var(--primary-color);
        }

        .sidebar .nav-link i {
            width: 20px;
            margin-right: 10px;
            text-align: center;
            font-size: 1rem;
        }

        .sidebar .nav-header {
            color: rgba(255, 255, 255, 0.5);
            padding: 1rem 1.5rem 0.5rem;
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.05em;
            font-weight: 600;
        }
        
        /* Content wrapper */
        #content-wrapper {
            flex: 1;
            margin-left: 280px;
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
        }
        
        /* Top navbar */
        .top-navbar {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            padding: 0.75rem 1.5rem;
            z-index: 1020;
            position: sticky;
            top: 0;
        }
        
        .navbar-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .navbar-left {
            display: flex;
            align-items: center;
        }
        
        .navbar-right {
            display: flex;
            align-items: center;
        }
        
        .sidebar-toggle-btn {
            background: none;
            border: none;
            color: var(--secondary-color);
            cursor: pointer;
            margin-right: 1rem;
            padding: 0.5rem;
            display: none;
        }
        
        /* Menu horizontal */
        .horizontal-menu {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
            margin-left: 1rem;
        }
        
        .horizontal-menu-item {
            margin-right: 1.5rem;
            position: relative;
        }
        
        .horizontal-menu-item a {
            color: var(--secondary-color);
            text-decoration: none;
            font-weight: 500;
            display: flex;
            align-items: center;
            padding: 0.5rem 0;
            position: relative;
        }
        
        .horizontal-menu-item a i {
            margin-right: 0.5rem;
        }
        
        .horizontal-menu-item.active a {
            color: var(--primary-color);
        }
        
        .horizontal-menu-item.active a::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 2px;
            background-color: var(--primary-color);
        }
        
        /* Avatar et menu utilisateur */
        .avatar-container {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            overflow: hidden;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: 600;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        .avatar-img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .avatar-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
        }
        
        .user-dropdown .dropdown-toggle::after {
            display: none;
        }
        
        .dropdown-user-details {
            padding: 1rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            border-bottom: 1px solid #e9ecef;
        }
        
        .user-avatar-dropdown {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            overflow: hidden;
            background-color: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .user-avatar-dropdown img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .avatar-placeholder-lg {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: 600;
        }
        
        .user-info-dropdown {
            flex: 1;
        }
        
        .user-name {
            margin-bottom: 0.25rem;
            font-weight: 600;
        }
        
        .user-email {
            margin-bottom: 0.5rem;
            font-size: 0.875rem;
            color: var(--secondary-color);
        }
        
        .user-role {
            font-size: 0.75rem;
        }
        
        /* Notifications */
        .notifications-dropdown {
            width: 320px;
            padding: 0;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .notification-item {
            display: flex;
            align-items: flex-start;
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #e9ecef;
            transition: background-color 0.2s ease;
        }
        
        .notification-item:hover {
            background-color: #f8f9fa;
        }
        
        .notification-icon {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            margin-right: 0.75rem;
            flex-shrink: 0;
        }
        
        .notification-content {
            flex: 1;
        }
        
        .notification-text {
            margin-bottom: 0.25rem;
            font-size: 0.875rem;
        }
        
        .notification-time {
            font-size: 0.75rem;
            color: var(--secondary-color);
            margin: 0;
        }
        
        /* Main content */
        #content {
            flex: 1;
            padding: 1.5rem;
            background-color: #f8fafc;
        }
        
        /* Footer */
        .sticky-footer {
            padding: 1rem 0;
            margin-top: auto;
            background-color: white;
            border-top: 1px solid #e9ecef;
            font-size: 0.875rem;
            color: var(--secondary-color);
            text-align: center;
        }
        
        /* Responsive */
        @media (max-width: 992px) {
            .sidebar {
                transform: translateX(-280px);
                width: 250px;
                z-index: 1050;
                box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
            }
            
            .sidebar-header {
                justify-content: center;
                padding: 1rem;
            }
            
            .sidebar-logo {
                height: 28px;
                margin-right: 0;
            }
            
            .sidebar-title {
                display: none;
            }
            
            #content-wrapper {
                margin-left: 0;
                width: 100%;
                transition: all 0.3s ease;
            }
            
            .sidebar-toggle-btn {
                display: block;
            }
            
            .sidebar.active {
                transform: translateX(0);
            }
            
            .horizontal-menu {
                display: none;
            }
            
            body.sidebar-collapsed::after {
                content: '';
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.4);
                z-index: 1040;
            }
        }
        
        @media (max-width: 768px) {
            .top-navbar {
                padding: 0.5rem 1rem;
            }
            
            #content {
                padding: 1rem;
            }
            
            .sidebar {
                width: 220px;
            }
            
            .sidebar-user {
                padding: 1rem;
            }
            
            .sidebar-avatar {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }
            
            .sidebar-user-name {
                font-size: 0.85rem;
            }
            
            .sidebar-user-role {
                font-size: 0.75rem;
            }
            
            .nav-link {
                padding: 0.6rem 1.2rem;
                font-size: 0.9rem;
            }
        }
        
        @media (max-width: 576px) {
            .dropdown-user-details {
                flex-direction: column;
                text-align: center;
            }
            
            #content {
                padding: 0.75rem;
            }
            
            .sidebar {
                width: 200px;
            }
            
            .sidebar-header {
                padding: 0.75rem;
            }
            
            .sidebar-logo {
                height: 24px;
            }
            
            .sidebar-user {
                padding: 0.75rem;
            }
            
            .sidebar-avatar {
                width: 36px;
                height: 36px;
            }
            
            .nav-link {
                padding: 0.5rem 1rem;
                font-size: 0.85rem;
            }
            
            .nav-link i {
                font-size: 1rem;
                min-width: 20px;
            }
        }
    </style>
</head>
<body>
    <!-- Sidebar Toggle Button -->
    <button id="sidebar-toggle" class="btn btn-light d-lg-none" style="position: fixed; top: 10px; left: 10px; z-index: 1060; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
        <i class="fas fa-bars"></i>
    </button>

    <!-- Sidebar -->
    <nav class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <img src="<?php echo e(asset('assets/images/logo_mini_gradis.png')); ?>" alt="Logo" class="logo-img">
        </div>
        <ul class="nav flex-column">
            <li class="nav-item">
                <a href="<?php echo e(route('cement-manager.dashboard')); ?>" class="nav-link <?php echo e(request()->routeIs('cement-manager.dashboard') ? 'active' : ''); ?>">
                    <i class="fas fa-home"></i>
                    <span>Tableau de bord</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="<?php echo e(url('/cement-manager')); ?>" class="nav-link <?php echo e(request()->is('cement-manager') || request()->routeIs('cement-manager.supplies.*') ? 'active' : ''); ?>">
                    <i class="fas fa-truck-loading"></i>
                    <span>Bons de Commande</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="<?php echo e(route('cement-manager.sales.create')); ?>" class="nav-link <?php echo e(request()->routeIs('cement-manager.sales.*') ? 'active' : ''); ?>">
                    <i class="fas fa-cash-register"></i>
                    <span>Vente</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="<?php echo e(route('cement-manager.sales.index')); ?>" class="nav-link <?php echo e(request()->routeIs('cement-manager.sales.index') ? 'active' : ''); ?>">
                    <i class="fas fa-history"></i>
                    <span>Historique des ventes</span>
                </a>
            </li>
            <li class="nav-item">
                <a href="<?php echo e(route('logout')); ?>" class="nav-link" 
                   onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Déconnexion</span>
                </a>
                <form id="logout-form" action="<?php echo e(route('logout')); ?>" method="POST" class="d-none">
                    <?php echo csrf_field(); ?>
                </form>
            </li>
        </ul>
    </nav>

    <div id="wrapper">
        <!-- Sidebar -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <img src="<?php echo e(asset('assets/images/logo_mini_gradis.png')); ?>" alt="Logo" class="sidebar-logo">
                <h5 class="sidebar-title">GRADIS</h5>
            </div>
            
            <!-- User Info in Sidebar -->
            <div class="sidebar-user">
                <div class="sidebar-avatar">
                    <?php if(Auth::user()->avatar && file_exists(public_path(Auth::user()->avatar))): ?>
                        <img src="<?php echo e(asset(Auth::user()->avatar)); ?>" alt="Avatar">
                    <?php else: ?>
                        <?php echo e(substr(Auth::user()->name, 0, 1)); ?>

                    <?php endif; ?>
                </div>
                <div class="sidebar-user-info">
                    <h6 class="sidebar-user-name"><?php echo e(Auth::user()->first_name); ?> <?php echo e(Auth::user()->last_name); ?></h6>
                    <p class="sidebar-user-role">Gestionnaire de Ciment</p>
                </div>
            </div>
            
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a href="<?php echo e(route('cement-manager.dashboard')); ?>" class="nav-link <?php echo e(request()->routeIs('cement-manager.dashboard') ? 'active' : ''); ?>">
                        <i class="fas fa-home"></i>
                        <span>Tableau de bord</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo e(url('/cement-manager')); ?>" class="nav-link <?php echo e(request()->is('cement-manager') || request()->routeIs('cement-manager.supplies.*') ? 'active' : ''); ?>">
                        <i class="fas fa-truck-loading"></i>
                        <span>Bons de Commande</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo e(route('cement-manager.sales.create')); ?>" class="nav-link <?php echo e(request()->routeIs('cement-manager.sales.create') ? 'active' : ''); ?>">
                        <i class="fas fa-cash-register"></i>
                        <span>Vente</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a href="<?php echo e(route('cement-manager.sales.index')); ?>" class="nav-link <?php echo e(request()->routeIs('cement-manager.sales.index') ? 'active' : ''); ?>">
                        <i class="fas fa-history"></i>
                        <span>Historique des ventes</span>
                    </a>
                </li>
            </ul>
        </nav>
        
        <!-- Content Wrapper -->
        <div id="content-wrapper">
            <!-- Top Navbar -->
            <nav class="top-navbar">
                <div class="navbar-content">
                    <div class="navbar-left">
                        <button class="sidebar-toggle-btn d-md-none">
                            <i class="fas fa-bars"></i>
                        </button>
                        <h5 class="mb-0"><?php echo $__env->yieldContent('title'); ?></h5>
                    </div>
                    <div class="navbar-right">
                        <!-- Dropdown User Info -->
                        <div class="dropdown user-dropdown">
                            <a href="#" class="dropdown-toggle" id="userDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                                <div class="avatar-container">
                                    <?php if(Auth::user()->avatar && file_exists(public_path(Auth::user()->avatar))): ?>
                                        <img src="<?php echo e(asset(Auth::user()->avatar)); ?>" alt="Avatar" class="avatar-img">
                                    <?php else: ?>
                                        <div class="avatar-placeholder">
                                            <?php echo e(substr(Auth::user()->name, 0, 1)); ?>

                                        </div>
                                    <?php endif; ?>
                                </div>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <li>
                                    <div class="dropdown-user-details">
                                        <div class="user-avatar-dropdown">
                                            <?php if(Auth::user()->avatar && file_exists(public_path(Auth::user()->avatar))): ?>
                                                <img src="<?php echo e(asset(Auth::user()->avatar)); ?>" alt="Avatar">
                                            <?php else: ?>
                                                <div class="avatar-placeholder-lg">
                                                    <?php echo e(substr(Auth::user()->name, 0, 1)); ?>

                                                </div>
                                            <?php endif; ?>
                                        </div>
                                        <div class="user-info-dropdown">
                                            <h6 class="user-name"><?php echo e(Auth::user()->first_name); ?> <?php echo e(Auth::user()->last_name); ?></h6>
                                            <p class="user-email"><?php echo e(Auth::user()->email); ?></p>
                                            <span class="badge bg-primary user-role">Gestionnaire de Ciment</span>
                                        </div>
                                    </div>
                                </li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('cement-manager.profile.show')); ?>"><i class="fas fa-user me-2"></i> Mon Profil</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('cement-manager.profile.edit')); ?>"><i class="fas fa-edit me-2"></i> Modifier Profil</a></li>
                                <li><a class="dropdown-item" href="<?php echo e(route('cement-manager.profile.password')); ?>"><i class="fas fa-key me-2"></i> Changer Mot de Passe</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <a class="dropdown-item" href="<?php echo e(route('logout')); ?>" onclick="event.preventDefault(); document.getElementById('logout-form-dropdown').submit();">
                                        <i class="fas fa-sign-out-alt me-2"></i> Déconnexion
                                    </a>
                                    <form id="logout-form-dropdown" action="<?php echo e(route('logout')); ?>" method="POST" class="d-none">
                                        <?php echo csrf_field(); ?>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </nav>

            <!-- Main Content -->
            <div id="content">
                <?php if(session('success')): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo e(session('success')); ?>

                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if(session('error')): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo e(session('error')); ?>

                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php echo $__env->yieldContent('content'); ?>
            </div>
            
            <!-- Footer -->
            <footer class="sticky-footer">
                <div class="container">
                    <div class="copyright text-center">
                        <span>&copy; 2025 GRADIS. Tous droits réservés. Développé par MOMK-Solutions</span>
                    </div>
                </div>
            </footer>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Responsive Sidebar Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const sidebar = document.getElementById('sidebar');
            const sidebarToggle = document.getElementById('sidebar-toggle');
            const contentWrapper = document.getElementById('content-wrapper');
            
            // Fonction pour basculer la sidebar
            function toggleSidebar() {
                sidebar.classList.toggle('active');
                document.body.classList.toggle('sidebar-collapsed');
                
                // Changer l'icône du bouton
                if (sidebar.classList.contains('active')) {
                    sidebarToggle.innerHTML = '<i class="fas fa-times"></i>';
                } else {
                    sidebarToggle.innerHTML = '<i class="fas fa-bars"></i>';
                }
            }
            
            // Ajouter l'événement de clic au bouton de bascule
            sidebarToggle.addEventListener('click', function(e) {
                e.stopPropagation();
                toggleSidebar();
            });
            
            // Fermer la sidebar sur mobile quand on clique en dehors
            document.addEventListener('click', function(event) {
                if (window.innerWidth <= 992 && 
                    !sidebar.contains(event.target) && 
                    !sidebarToggle.contains(event.target) &&
                    sidebar.classList.contains('active')) {
                    toggleSidebar();
                }
            });
            
            // Empêcher la propagation des clics dans la sidebar
            sidebar.addEventListener('click', function(e) {
                e.stopPropagation();
            });
            
            // Ajuster la sidebar lors du redimensionnement
            window.addEventListener('resize', function() {
                if (window.innerWidth > 992) {
                    sidebar.classList.remove('active');
                    document.body.classList.remove('sidebar-collapsed');
                    sidebarToggle.innerHTML = '<i class="fas fa-bars"></i>';
                }
            });
            
            // Cacher automatiquement la sidebar sur les très petits écrans au chargement
            if (window.innerWidth <= 576) {
                sidebar.classList.remove('active');
                document.body.classList.remove('sidebar-collapsed');
            }
        });
    </script>

    <?php echo $__env->yieldPushContent('scripts'); ?>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\gradis\resources\views/layouts/cement_manager.blade.php ENDPATH**/ ?>
<!-- Actions en lot -->
<div class="bulk-actions-bar" id="bulkActionsBar" style="display: none;">
    <div class="container-fluid">
        <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
                <i class="fas fa-check-circle me-2"></i>
                <span id="selectedCount">0</span> vente(s) sélectionnée(s)
            </div>
            <div class="btn-group">
                <button class="btn btn-outline-primary btn-sm" onclick="exportSelected()" data-bs-toggle="tooltip" title="Exporter les ventes sélectionnées">
                    <i class="fas fa-download me-1"></i>Exporter
                </button>
                <button class="btn btn-outline-success btn-sm" onclick="markAsPaid()" data-bs-toggle="tooltip" title="Marquer comme payées">
                    <i class="fas fa-check me-1"></i>Marquer payé
                </button>
                <button class="btn btn-outline-info btn-sm" onclick="printInvoices()" data-bs-toggle="tooltip" title="Imprimer les factures">
                    <i class="fas fa-print me-1"></i>Imprimer
                </button>
                <button class="btn btn-outline-danger btn-sm" onclick="deleteSelected()" data-bs-toggle="tooltip" title="Supprimer les ventes sélectionnées">
                    <i class="fas fa-trash me-1"></i>Supprimer
                </button>
                <button class="btn btn-outline-secondary btn-sm" onclick="clearSelection()" data-bs-toggle="tooltip" title="Désélectionner tout">
                    <i class="fas fa-times me-1"></i>Annuler
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Modal de confirmation pour les actions en lot -->
<div class="modal fade" id="bulkActionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkActionModalTitle">
                    <i class="fas fa-exclamation-triangle me-2"></i>Confirmer l'action
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="bulkActionModalBody">
                <!-- Le contenu sera injecté dynamiquement -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" id="confirmBulkAction">Confirmer</button>
            </div>
        </div>
    </div>
</div>

<script>
// Fonctions pour les actions en lot
function markAsPaid() {
    const selectedIds = getSelectedSaleIds();
    if (selectedIds.length === 0) {
        showAlert('Aucune sélection', 'Veuillez sélectionner au moins une vente.', 'info');
        return;
    }
    
    showBulkActionModal(
        'Marquer comme payées',
        `Êtes-vous sûr de vouloir marquer ${selectedIds.length} vente(s) comme payées ?`,
        'success',
        () => {
            performBulkAction('/admin/sales/bulk-mark-paid', selectedIds);
        }
    );
}

function printInvoices() {
    const selectedIds = getSelectedSaleIds();
    if (selectedIds.length === 0) {
        showAlert('Aucune sélection', 'Veuillez sélectionner au moins une vente.', 'info');
        return;
    }
    
    // Ouvrir les factures dans de nouveaux onglets
    selectedIds.forEach(id => {
        window.open(`/admin/sales/${id}/invoice`, '_blank');
    });
}

function clearSelection() {
    document.querySelectorAll('.sale-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    document.getElementById('selectAll').checked = false;
    document.getElementById('selectAll').indeterminate = false;
    updateBulkActions();
}

function getSelectedSaleIds() {
    return Array.from(document.querySelectorAll('.sale-checkbox:checked'))
        .map(checkbox => checkbox.value);
}

function showBulkActionModal(title, message, type, callback) {
    const modal = new bootstrap.Modal(document.getElementById('bulkActionModal'));
    const titleElement = document.getElementById('bulkActionModalTitle');
    const bodyElement = document.getElementById('bulkActionModalBody');
    const confirmButton = document.getElementById('confirmBulkAction');
    
    // Définir l'icône selon le type
    let icon = 'fas fa-exclamation-triangle';
    let buttonClass = 'btn-primary';
    
    switch(type) {
        case 'danger':
            icon = 'fas fa-exclamation-triangle';
            buttonClass = 'btn-danger';
            break;
        case 'success':
            icon = 'fas fa-check-circle';
            buttonClass = 'btn-success';
            break;
        case 'info':
            icon = 'fas fa-info-circle';
            buttonClass = 'btn-info';
            break;
    }
    
    titleElement.innerHTML = `<i class="${icon} me-2"></i>${title}`;
    bodyElement.innerHTML = message;
    
    // Réinitialiser les classes du bouton
    confirmButton.className = `btn ${buttonClass}`;
    
    // Définir le callback
    confirmButton.onclick = () => {
        callback();
        modal.hide();
    };
    
    modal.show();
}

function performBulkAction(url, saleIds, additionalData = {}) {
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = url;
    form.style.display = 'none';
    
    // Token CSRF
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = '_token';
    csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    form.appendChild(csrfToken);
    
    // IDs des ventes
    saleIds.forEach(id => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'sale_ids[]';
        input.value = id;
        form.appendChild(input);
    });
    
    // Données additionnelles
    Object.keys(additionalData).forEach(key => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = additionalData[key];
        form.appendChild(input);
    });
    
    document.body.appendChild(form);
    form.submit();
}

function showAlert(title, message, type) {
    Swal.fire({
        title: title,
        text: message,
        icon: type,
        confirmButtonText: 'OK'
    });
}
</script>
<?php /**PATH C:\xampp\htdocs\gradis\resources\views/admin/sales/partials/bulk-actions.blade.php ENDPATH**/ ?>
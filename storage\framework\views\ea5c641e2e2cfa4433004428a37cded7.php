<?php $__env->startSection('title', 'Historique des ventes'); ?>

<?php $__env->startSection('content'); ?>
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Historique des ventes</h1>
        <a href="<?php echo e(route('cement-manager.sales.create')); ?>" class="btn btn-primary">
            <i class="fas fa-plus-circle me-1"></i> Nouvelle vente
        </a>
    </div>
    
    <?php if(isset($rejectedSales) && $rejectedSales->count() > 0): ?>
    <div class="card border-danger shadow mb-4">
        <div class="card-header bg-danger text-white d-flex align-items-center justify-content-between">
            <span><i class="fas fa-exclamation-triangle me-2"></i>Ventes rejetées par l'administrateur</span>
            <span class="badge bg-dark"><?php echo e($rejectedSales->count()); ?> rejet(s)</span>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover table-bordered m-0 align-middle">
                    <thead class="table-danger">
                        <tr>
                            <th>#</th>
                            <th>Date</th>
                            <th>Client</th>
                            <th>Quantité</th>
                            <th>Type</th>
                            <th>Raison du rejet</th>
                            <th class="text-center">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $rejectedSales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <tr>
                                <td><strong><?php echo e($sale->id); ?></strong></td>
                                <td><?php echo e($sale->created_at->format('d/m/Y H:i')); ?></td>
                                <td><?php echo e($sale->customer_name); ?></td>
                                <td><?php echo e($sale->quantity); ?> T</td>
                                <td>
                                    <?php if(($sale->discount_per_ton ?? 0) > 0): ?>
                                        <span class="badge bg-info">Remise</span>
                                    <?php elseif($sale->price_modified && $sale->original_price): ?>
                                        <span class="badge bg-warning">Augmentation de prix</span>
                                    <?php else: ?>
                                        <span class="badge bg-secondary">Standard</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <span class="text-danger"><?php echo e(Str::limit($sale->admin_note, 50)); ?></span>
                                </td>
                                <td class="text-center">
                                    <a href="<?php echo e(route('cement-manager.sales.show', $sale)); ?>" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> Détail
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="card shadow">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Référence BC</th>
                            <th>Client</th>
                            <th>Ville</th>
                            <th>Quantité</th>
                            <th>Montant</th>
                            <th>Paiement</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $sales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td><?php echo e($sale->created_at->format('d/m/Y H:i')); ?></td>
                                <td><?php echo e($sale->supply->reference); ?></td>
                                <td>
                                    <?php echo e($sale->customer_name); ?>

                                    <br>
                                    <small class="text-muted"><?php echo e($sale->customer_phone); ?></small>
                                </td>
                                <td><?php echo e($sale->city->name); ?></td>
                                <td><?php echo e(number_format($sale->quantity, 2, ',', ' ')); ?> T</td>
                                <td><?php echo e(number_format($sale->total_amount, 0, ',', ' ')); ?> FCFA</td>
                                <td>
                                    <?php if($sale->payment_method === 'cash'): ?>
                                        <span class="badge bg-success">Comptant</span>
                                    <?php else: ?>
                                        <span class="badge bg-warning">Crédit</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if($sale->status === 'completed'): ?>
                                        <span class="badge bg-success">Terminé</span>
                                    <?php elseif($sale->status === 'pending'): ?>
                                        <span class="badge bg-warning">En attente</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">Annulé</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <a href="<?php echo e(route('cement-manager.sales.show', $sale)); ?>" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i> Détail
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="9" class="text-center">Aucune vente trouvée</td>
                            </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>

            <div class="d-flex justify-content-end mt-3">
                <?php echo e($sales->links()); ?>

            </div>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.cement_manager', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/cement-manager/sales/index.blade.php ENDPATH**/ ?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facture #<?php echo e($sale->id); ?> - GRADIS</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .invoice-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 40px;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
        }
        
        .company-info h1 {
            color: #007bff;
            margin: 0;
            font-size: 2.5em;
        }
        
        .company-info p {
            margin: 5px 0;
            color: #666;
        }
        
        .invoice-number {
            text-align: right;
        }
        
        .invoice-number h2 {
            color: #333;
            margin: 0;
            font-size: 2em;
        }
        
        .invoice-details {
            display: flex;
            justify-content: space-between;
            margin-bottom: 40px;
        }
        
        .client-info, .sale-info {
            flex: 1;
        }
        
        .client-info {
            margin-right: 40px;
        }
        
        .info-section h3 {
            color: #007bff;
            border-bottom: 2px solid #e9ecef;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        
        .info-section p {
            margin: 8px 0;
            color: #333;
        }
        
        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        
        .invoice-table th {
            background: #007bff;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: bold;
        }
        
        .invoice-table td {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .invoice-table tr:nth-child(even) {
            background: #f8f9fa;
        }
        
        .total-section {
            text-align: right;
            margin-top: 30px;
        }
        
        .total-row {
            display: flex;
            justify-content: flex-end;
            margin-bottom: 10px;
        }
        
        .total-label {
            width: 200px;
            text-align: right;
            padding-right: 20px;
            font-weight: bold;
        }
        
        .total-amount {
            width: 150px;
            text-align: right;
        }
        
        .final-total {
            border-top: 2px solid #007bff;
            padding-top: 10px;
            font-size: 1.2em;
            font-weight: bold;
            color: #007bff;
        }
        
        .status-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 0.9em;
        }
        
        .status-paid {
            background: #d4edda;
            color: #155724;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-cancelled {
            background: #f8d7da;
            color: #721c24;
        }
        
        .footer {
            margin-top: 50px;
            text-align: center;
            color: #666;
            border-top: 1px solid #e9ecef;
            padding-top: 20px;
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .invoice-container {
                box-shadow: none;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <!-- En-tête de la facture -->
        <div class="invoice-header">
            <div class="company-info">
                <h1>GRADIS</h1>
                <p>Gestion et Redistribution Automatisée</p>
                <p>des Infrastructures de Stockage</p>
                <p>Tél: +228 XX XX XX XX</p>
                <p>Email: <EMAIL></p>
            </div>
            <div class="invoice-number">
                <h2>FACTURE</h2>
                <p><strong>#<?php echo e(str_pad($sale->id, 6, '0', STR_PAD_LEFT)); ?></strong></p>
                <p><?php echo e($sale->created_at->format('d/m/Y')); ?></p>
            </div>
        </div>

        <!-- Détails de la facture -->
        <div class="invoice-details">
            <div class="client-info info-section">
                <h3>Informations Client</h3>
                <p><strong>Nom:</strong> <?php echo e($sale->customer_name ?? 'N/A'); ?></p>
                <?php if($sale->customer_phone): ?>
                    <p><strong>Téléphone:</strong> <?php echo e($sale->customer_phone); ?></p>
                <?php endif; ?>
                <?php if($sale->customer_address): ?>
                    <p><strong>Adresse:</strong> <?php echo e($sale->customer_address); ?></p>
                <?php endif; ?>
                <p><strong>Statut:</strong> 
                    <?php switch($sale->payment_status):
                        case ('paid'): ?>
                            <span class="status-badge status-paid">Payé</span>
                            <?php break; ?>
                        <?php case ('pending'): ?>
                            <span class="status-badge status-pending">En attente</span>
                            <?php break; ?>
                        <?php case ('cancelled'): ?>
                            <span class="status-badge status-cancelled">Annulé</span>
                            <?php break; ?>
                    <?php endswitch; ?>
                </p>
            </div>
            
            <div class="sale-info info-section">
                <h3>Détails de la Vente</h3>
                <p><strong>Date de vente:</strong> <?php echo e($sale->created_at->format('d/m/Y H:i')); ?></p>
                <p><strong>Vendeur:</strong> <?php echo e($sale->user->name ?? 'N/A'); ?></p>
                <?php if($sale->delivery_date): ?>
                    <p><strong>Date de livraison:</strong> <?php echo e(\Carbon\Carbon::parse($sale->delivery_date)->format('d/m/Y')); ?></p>
                <?php endif; ?>
                <?php if($sale->delivery_address): ?>
                    <p><strong>Adresse de livraison:</strong> <?php echo e($sale->delivery_address); ?></p>
                <?php endif; ?>
            </div>
        </div>

        <!-- Tableau des articles -->
        <table class="invoice-table">
            <thead>
                <tr>
                    <th>Produit</th>
                    <th>Quantité</th>
                    <th>Prix unitaire</th>
                    <th>Total</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>
                        <strong><?php echo e($sale->product->name ?? 'Produit non trouvé'); ?></strong>
                        <?php if($sale->product && $sale->product->description): ?>
                            <br><small style="color: #666;"><?php echo e($sale->product->description); ?></small>
                        <?php endif; ?>
                    </td>
                    <td><?php echo e($sale->quantity); ?> tonnes</td>
                    <td><?php echo e(number_format($sale->unit_price, 0, ',', ' ')); ?> FCFA</td>
                    <td><?php echo e(number_format($sale->quantity * $sale->unit_price, 0, ',', ' ')); ?> FCFA</td>
                </tr>
            </tbody>
        </table>

        <!-- Section des totaux -->
        <div class="total-section">
            <div class="total-row">
                <div class="total-label">Sous-total:</div>
                <div class="total-amount"><?php echo e(number_format($sale->quantity * $sale->unit_price, 0, ',', ' ')); ?> FCFA</div>
            </div>
            
            <?php if($sale->discount_total && $sale->discount_total > 0): ?>
                <div class="total-row">
                    <div class="total-label">Remise:</div>
                    <div class="total-amount">-<?php echo e(number_format($sale->discount_total, 0, ',', ' ')); ?> FCFA</div>
                </div>
            <?php endif; ?>
            
            <div class="total-row final-total">
                <div class="total-label">TOTAL À PAYER:</div>
                <div class="total-amount"><?php echo e(number_format(($sale->quantity * $sale->unit_price) - ($sale->discount_total ?? 0), 0, ',', ' ')); ?> FCFA</div>
            </div>
        </div>

        <!-- Pied de page -->
        <div class="footer">
            <p>Merci pour votre confiance !</p>
            <p>Cette facture a été générée automatiquement le <?php echo e(now()->format('d/m/Y à H:i')); ?></p>
            <p><strong>GRADIS</strong> - Votre partenaire de confiance pour la gestion des matériaux de construction</p>
        </div>
    </div>

    <script>
        // Imprimer automatiquement la facture
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>
<?php /**PATH C:\xampp\htdocs\gradis\resources\views/admin/sales/invoice.blade.php ENDPATH**/ ?>
<div class="card border-info shadow mb-4 pending-validation-card" id="discount-validation-section">
    <div class="card-header bg-info text-white d-flex align-items-center justify-content-between">
        <span><i class="fas fa-arrow-down me-2"></i>Ventes avec remise à valider</span>
        <span class="badge bg-dark border border-light" style="border: 2px solid #ffffff !important; box-shadow: 0 2px 4px rgba(0,0,0,0.2);"><?php echo e($pendingDiscountSales->count()); ?> à valider</span>
    </div>
    <div class="card-body p-0">
        <div class="table-responsive">
            <table class="table table-hover table-bordered m-0 align-middle">
                <thead class="table-primary">
                    <tr>
                        <th>#</th>
                        <th>Date</th>
                        <th>Client</th>
                        <th>Quantité</th>
                        <th>Remise/tonne</th>
                        <th>Montant remise</th>
                        <th>Montant total</th>
                        <th class="text-center">Action</th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $pendingDiscountSales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td><strong><?php echo e($sale->id); ?></strong></td>
                            <td><?php echo e($sale->created_at->format('d/m/Y H:i')); ?></td>
                            <td><?php echo e($sale->customer_name); ?></td>
                            <td><?php echo e($sale->quantity); ?></td>
                            <td class="text-danger fw-bold"><?php echo e(number_format($sale->discount_per_ton, 2, ',', ' ')); ?> FCFA</td>
                            <td class="text-danger"><?php echo e(number_format($sale->discount_total, 2, ',', ' ')); ?> FCFA</td>
                            <td class="fw-bold"><?php echo e(number_format($sale->total_amount, 2, ',', ' ')); ?> FCFA</td>
                            <td class="text-center">
                                <form action="<?php echo e(route('admin.sales.validate-discount', $sale)); ?>" method="POST" class="d-inline">
                                    <?php echo csrf_field(); ?>
                                    <input type="hidden" name="action" value="approve">
                                    <button type="submit" class="btn btn-outline-success btn-sm" title="Valider">
                                        <i class="fas fa-check"></i>
                                    </button>
                                </form>
                                <button type="button" class="btn btn-outline-danger btn-sm reject-discount-sale" 
                                    data-sale-id="<?php echo e($sale->id); ?>"
                                    data-quantity="<?php echo e($sale->quantity); ?>"
                                    data-customer="<?php echo e($sale->customer_name); ?>"
                                    title="Rejeter">
                                    <i class="fas fa-times"></i>
                                </button>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="8" class="text-center text-muted">Aucune vente à valider</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php $__env->startPush('scripts'); ?>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialiser les boutons de rejet
        const rejectButtons = document.querySelectorAll('.reject-discount-sale');
        
        rejectButtons.forEach(button => {
            button.addEventListener('click', function() {
                const saleId = this.getAttribute('data-sale-id');
                const quantity = this.getAttribute('data-quantity');
                const customer = this.getAttribute('data-customer');
                
                Swal.fire({
                    title: 'Rejeter la vente avec remise',
                    html: `<p>Vous êtes sur le point de rejeter une vente de <strong>${quantity} tonnes</strong> pour <strong>${customer}</strong>.</p>
                           <p>Veuillez indiquer la raison du rejet :</p>`,
                    input: 'textarea',
                    inputPlaceholder: 'Raison du rejet...',
                    inputAttributes: {
                        'aria-label': 'Raison du rejet',
                        'required': 'required'
                    },
                    showCancelButton: true,
                    confirmButtonColor: '#d33',
                    cancelButtonColor: '#3085d6',
                    confirmButtonText: 'Rejeter la vente',
                    cancelButtonText: 'Annuler',
                    inputValidator: (value) => {
                        if (!value) {
                            return 'Vous devez indiquer une raison pour le rejet';
                        }
                    }
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Créer un formulaire dynamique pour soumettre la demande
                        const form = document.createElement('form');
                        form.method = 'POST';
                        form.action = `/admin/sales/${saleId}/validate-discount`;
                        form.style.display = 'none';
                        
                        // Token CSRF
                        const csrfToken = document.createElement('input');
                        csrfToken.type = 'hidden';
                        csrfToken.name = '_token';
                        csrfToken.value = document.querySelector('meta[name="csrf-token"]').getAttribute('content');
                        form.appendChild(csrfToken);
                        
                        // Action (rejeter)
                        const actionInput = document.createElement('input');
                        actionInput.type = 'hidden';
                        actionInput.name = 'action';
                        actionInput.value = 'reject';
                        form.appendChild(actionInput);
                        
                        // Raison du rejet
                        const reasonInput = document.createElement('input');
                        reasonInput.type = 'hidden';
                        reasonInput.name = 'admin_note';
                        reasonInput.value = result.value;
                        form.appendChild(reasonInput);
                        
                        document.body.appendChild(form);
                        form.submit();
                    }
                });
            });
        });
    });
</script>
<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\gradis\resources\views/admin/sales/partials/pending_discount_sales.blade.php ENDPATH**/ ?>
<?php $__env->startSection('styles'); ?>
<link rel="stylesheet" href="<?php echo e(asset('css/cashier-dashboard-modern.css')); ?>">
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('title', 'Tableau de Bord Caissier'); ?>

<?php $__env->startSection('content'); ?>
<!-- Script pour confirmer que nous sommes dans la bonne vue -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        console.log('%c TABLEAU DE BORD CAISSIER MODERNISÉ ', 'background: #1E88E5; color: white; padding: 10px; font-size: 16px; font-weight: bold; border-radius: 5px;');
        console.log('Fichier: resources/views/cashier/dashboard.blade.php');
    });
</script>

<div class="container-fluid dashboard-container">
    <!-- En-tête du tableau de bord -->
    <div class="dashboard-header">
        <div>
            <h1 class="dashboard-title">
                <i class="fas fa-tachometer-alt"></i>
                <span>Tableau de Bord Caissier</span>
            </h1>
            <p class="dashboard-subtitle">Bienvenue <?php echo e(Auth::user()->name); ?> ! Voici un aperçu de l'activité du <?php echo e(now()->format('d/m/Y')); ?></p>
        </div>
        <div class="dashboard-actions">
            <button class="btn btn-outline-primary" onclick="window.print()">
                <i class="fas fa-print me-2"></i> Imprimer
            </button>
            <button class="btn btn-primary" onclick="window.location.reload()">
                <i class="fas fa-sync-alt me-2"></i> Actualiser
            </button>
        </div>
    </div>
    
    <!-- Actions rapides avec design moderne -->
    <div class="quick-actions">
        <div class="quick-action-card fade-in fade-in-delay-1" onclick="window.location.href='<?php echo e(route('cashier.payments.pending')); ?>'">
            <div class="quick-action-icon">
                <i class="fas fa-money-bill-wave"></i>
            </div>
            <div class="quick-action-title">Nouveau Paiement</div>
            <div class="quick-action-description">Enregistrer un nouveau paiement client</div>
        </div>
        
        <div class="quick-action-card fade-in fade-in-delay-2" onclick="window.location.href='<?php echo e(route('cashier.sales.index')); ?>'">
            <div class="quick-action-icon">
                <i class="fas fa-shopping-cart"></i>
            </div>
            <div class="quick-action-title">Gestion des Ventes</div>
            <div class="quick-action-description">Consulter et gérer les ventes</div>
        </div>
        
        <div class="quick-action-card fade-in fade-in-delay-3" onclick="window.location.href='<?php echo e(route('cashier.payments.index')); ?>'">
            <div class="quick-action-icon">
                <i class="fas fa-receipt"></i>
            </div>
            <div class="quick-action-title">Historique Paiements</div>
            <div class="quick-action-description">Consulter l'historique des paiements</div>
        </div>
        
        <div class="quick-action-card fade-in fade-in-delay-4" onclick="window.location.href='<?php echo e(route('cashier.profile')); ?>'">
            <div class="quick-action-icon">
                <i class="fas fa-user-cog"></i>
            </div>
            <div class="quick-action-title">Mon Profil</div>
            <div class="quick-action-description">Gérer mon compte et mes préférences</div>
        </div>
    </div>

    <!-- Statistiques avec design moderne et informatif -->
    <div class="stats-overview">
        <!-- Ventes du jour -->
        <div class="stat-card fade-in fade-in-delay-1">
            <div class="stat-card-header">
                <div class="stat-card-icon sales">
                    <i class="fas fa-shopping-cart"></i>
                </div>
                <?php
                    $dailySalesChange = isset($stats['yesterday_sales_amount']) && $stats['yesterday_sales_amount'] > 0 
                        ? (($stats['daily_sales_amount'] - $stats['yesterday_sales_amount']) / $stats['yesterday_sales_amount']) * 100 
                        : 0;
                ?>
                <div class="stat-trend <?php echo e($dailySalesChange >= 0 ? 'positive' : 'negative'); ?>">
                    <i class="fas fa-<?php echo e($dailySalesChange >= 0 ? 'arrow-up' : 'arrow-down'); ?>"></i>
                    <span><?php echo e(abs(round($dailySalesChange))); ?>% <?php echo e($dailySalesChange >= 0 ? 'hausse' : 'baisse'); ?></span>
                </div>
            </div>
            <div class="stat-card-title">Ventes du jour</div>
            <div class="stat-card-value"><?php echo e(number_format($stats['daily_sales_amount'], 0, ',', ' ')); ?> FCFA</div>
            <div class="stat-card-subtitle"><?php echo e($stats['daily_sales_count']); ?> vente(s) aujourd'hui</div>
            <div class="stat-card-footer">
                <div class="progress-container">
                    <div class="progress-bar sales" style="width: <?php echo e(min(($stats['daily_sales_count'] / max(1, $stats['daily_sales_count'] + 5)) * 100, 100)); ?>%"></div>
                </div>
            </div>
        </div>

        <!-- Paiements du jour -->
        <div class="stat-card fade-in fade-in-delay-2">
            <div class="stat-card-header">
                <div class="stat-card-icon payments">
                    <i class="fas fa-money-bill-wave"></i>
                </div>
                <?php
                    $dailyPaymentsChange = isset($stats['yesterday_payments_amount']) && $stats['yesterday_payments_amount'] > 0 
                        ? (($stats['daily_payments_amount'] - $stats['yesterday_payments_amount']) / $stats['yesterday_payments_amount']) * 100 
                        : 0;
                ?>
                <div class="stat-trend <?php echo e($dailyPaymentsChange >= 0 ? 'positive' : 'negative'); ?>">
                    <i class="fas fa-<?php echo e($dailyPaymentsChange >= 0 ? 'arrow-up' : 'arrow-down'); ?>"></i>
                    <span><?php echo e(abs(round($dailyPaymentsChange))); ?>% <?php echo e($dailyPaymentsChange >= 0 ? 'hausse' : 'baisse'); ?></span>
                </div>
            </div>
            <div class="stat-card-title">Paiements du jour</div>
            <div class="stat-card-value"><?php echo e(number_format($stats['daily_payments_amount'], 0, ',', ' ')); ?> FCFA</div>
            <div class="stat-card-subtitle"><?php echo e($stats['daily_payments_count']); ?> paiement(s) aujourd'hui</div>
            <div class="stat-card-footer">
                <div class="progress-container">
                    <div class="progress-bar payments" style="width: <?php echo e(min(($stats['daily_payments_count'] / max(1, $stats['daily_payments_count'] + 5)) * 100, 100)); ?>%"></div>
                </div>
            </div>
        </div>

        <!-- Ventes à crédit -->
        <div class="stat-card fade-in fade-in-delay-3">
            <div class="stat-card-header">
                <div class="stat-card-icon credit">
                    <i class="fas fa-credit-card"></i>
                </div>
            </div>
            <div class="stat-card-title">Ventes à crédit</div>
            <div class="stat-card-value"><?php echo e($stats['unpaid_credit_sales'] + $stats['partially_paid_credit_sales']); ?></div>
            <div class="stat-card-subtitle">
                <span class="d-flex align-items-center gap-2">
                    <i class="fas fa-times-circle text-danger"></i> <?php echo e($stats['unpaid_credit_sales']); ?> impayées
                </span>
                <span class="d-flex align-items-center gap-2">
                    <i class="fas fa-clock text-warning"></i> <?php echo e($stats['partially_paid_credit_sales']); ?> partielles
                </span>
            </div>
            <div class="stat-card-footer">
                <div class="progress-container">
                    <div class="progress-bar credit" style="width: <?php echo e(min((($stats['unpaid_credit_sales'] + $stats['partially_paid_credit_sales']) / max(1, $stats['unpaid_credit_sales'] + $stats['partially_paid_credit_sales'] + 5)) * 100, 100)); ?>%"></div>
                </div>
            </div>
        </div>
        

    </div>

    <!-- Sections principales du tableau de bord -->
    <div class="dashboard-sections">
        <!-- Ventes récentes avec design moderne -->
        <div class="dashboard-section fade-in fade-in-delay-1">
            <div class="dashboard-section-header">
                <h2 class="dashboard-section-title">
                    <i class="fas fa-shopping-cart"></i>
                    Ventes récentes
                </h2>
                <a href="<?php echo e(route('cashier.sales.index')); ?>" class="view-all-button">
                    <span>Voir tout</span>
                    <i class="fas fa-arrow-right"></i>
                </a>
            </div>
            <div class="dashboard-section-body">
                <div class="table-responsive">
                    <table class="modern-table">
                        <thead>
                            <tr>
                                <th>Référence</th>
                                <th>Client</th>
                                <th>Montant</th>
                                <th>Statut</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $recent_sales; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sale): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <?php
                                // Déterminer la classe de ligne selon l'état de paiement
                                $rowClass = '';
                                $paidAmount = $sale->amount_paid ?? 0;
                                $totalAmount = $sale->total_amount;
                                $paymentProgress = $totalAmount > 0 ? ($paidAmount / $totalAmount) * 100 : 0;
                                
                                if ($sale->status == 'cancelled') {
                                    $rowClass = 'row-failed';
                                } else if ($paymentProgress >= 99.9) {
                                    $rowClass = 'row-paid';
                                } else if ($paymentProgress > 0) {
                                    $rowClass = 'row-partially-paid';
                                } else {
                                    $rowClass = 'row-not-paid';
                                }
                            ?>
                            <tr class="<?php echo e($rowClass); ?>">
                                <td>
                                    <div class="d-flex align-items-center gap-2">
                                        <?php if($rowClass == 'row-paid'): ?>
                                            <div class="payment-indicator paid" title="Paiement terminé">
                                                <i class="fas fa-check-circle"></i>
                                            </div>
                                        <?php elseif($rowClass == 'row-partially-paid'): ?>
                                            <div class="payment-indicator partially-paid" title="Paiement en cours">
                                                <i class="fas fa-clock"></i>
                                            </div>
                                        <?php elseif($rowClass == 'row-not-paid'): ?>
                                            <div class="payment-indicator not-paid" title="Paiement non démarré">
                                                <i class="fas fa-exclamation-circle"></i>
                                            </div>
                                        <?php elseif($rowClass == 'row-failed'): ?>
                                            <div class="payment-indicator failed" title="Vente annulée">
                                                <i class="fas fa-times-circle"></i>
                                            </div>
                                        <?php endif; ?>
                                        <div>
                                            <strong><?php echo e($sale->reference ?? ('VNT-' . str_pad($sale->id, 6, '0', STR_PAD_LEFT))); ?></strong>
                                            <div style="font-size: 0.75rem; color: var(--text-secondary);"><?php echo e($sale->created_at->format('d/m/Y H:i')); ?></div>
                                        </div>
                                    </div>
                                </td>
                                <td><?php echo e($sale->customer_name ?? 'Client non spécifié'); ?></td>
                                <td>
                                    <div class="d-flex flex-column">
                                        <span><?php echo e(number_format($sale->total_amount, 0, ',', ' ')); ?> FCFA</span>
                                        <?php if($paymentProgress > 0 && $paymentProgress < 100): ?>
                                            <small class="text-success">Payé: <?php echo e(number_format($paidAmount, 0, ',', ' ')); ?> FCFA</small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <?php switch($sale->payment_status ?? ($paymentProgress >= 99.9 ? 'paid' : ($paymentProgress > 0 ? 'partial' : 'unpaid'))):
                                        case ('paid'): ?>
                                            <span class="status-badge paid">
                                                <i class="fas fa-check-circle"></i>
                                                <span>Payé</span>
                                            </span>
                                            <?php break; ?>
                                        <?php case ('partial'): ?>
                                            <span class="status-badge pending">
                                                <i class="fas fa-clock"></i>
                                                <span>Partiel</span>
                                            </span>
                                            <?php break; ?>
                                        <?php case ('unpaid'): ?>
                                            <span class="status-badge failed">
                                                <i class="fas fa-times-circle"></i>
                                                <span>Impayé</span>
                                            </span>
                                            <?php break; ?>
                                        <?php default: ?>
                                            <span class="status-badge">
                                                <i class="fas fa-question-circle"></i>
                                                <span><?php echo e($sale->payment_status); ?></span>
                                            </span>
                                    <?php endswitch; ?>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="<?php echo e(route('cashier.sales.show', $sale)); ?>" class="action-button view" title="Voir les détails">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if($paymentProgress < 99.9): ?>
                                        <a href="<?php echo e(route('cashier.payments.pending')); ?>" class="action-button edit" title="Ajouter un paiement">
                                            <i class="fas fa-money-bill-wave"></i>
                                        </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="5" class="text-center py-4">
                                    <div class="py-5">
                                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                        <h4>Aucune vente récente</h4>
                                        <p class="text-muted">Les ventes récentes apparaîtront ici</p>
                                    </div>
                                </td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Derniers paiements avec design moderne -->
        <div class="dashboard-section fade-in fade-in-delay-2">
            <div class="dashboard-section-header">
                <h2 class="dashboard-section-title">
                    <i class="fas fa-money-bill-wave"></i>
                    Derniers paiements
                </h2>
                <a href="<?php echo e(route('cashier.payments.index')); ?>" class="view-all-button">
                    <span>Voir tout</span>
                    <i class="fas fa-arrow-right"></i>
                </a>
            </div>
            <div class="dashboard-section-body">
                <div class="table-responsive">
                    <table class="modern-table">
                        <thead>
                            <tr>
                                <th>Client</th>
                                <th>Montant</th>
                                <th>Méthode</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php $__empty_1 = true; $__currentLoopData = $latest_payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center gap-2">
                                        <div class="payment-method-icon <?php echo e($payment->payment_method ?? 'cash'); ?>">
                                            <i class="fas fa-<?php echo e($payment->payment_method == 'bank_transfer' ? 'university' : ($payment->payment_method == 'check' ? 'money-check-alt' : 'coins')); ?>"></i>
                                        </div>
                                        <div>
                                            <?php if($payment->sale): ?>
                                                <strong><?php echo e($payment->sale->customer_name ?? 'Client'); ?></strong>
                                                <?php if($payment->sale->reference): ?>
                                                <div style="font-size: 0.75rem; color: var(--text-secondary);">Réf: <?php echo e($payment->sale->reference); ?></div>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="text-muted">Client non spécifié</span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex flex-column">
                                        <span class="text-primary fw-bold"><?php echo e(number_format($payment->amount, 0, ',', ' ')); ?> FCFA</span>
                                        <?php if($payment->sale): ?>
                                            <small class="text-muted">Sur <?php echo e(number_format($payment->sale->total_amount, 0, ',', ' ')); ?> FCFA</small>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td>
                                    <?php switch($payment->payment_method ?? 'cash'):
                                        case ('cash'): ?>
                                            <span class="status-badge paid">
                                                <i class="fas fa-coins"></i>
                                                <span>Espèces</span>
                                            </span>
                                            <?php break; ?>
                                        <?php case ('bank_transfer'): ?>
                                            <span class="status-badge">
                                                <i class="fas fa-university"></i>
                                                <span>Virement</span>
                                            </span>
                                            <?php break; ?>
                                        <?php case ('check'): ?>
                                            <span class="status-badge pending">
                                                <i class="fas fa-money-check-alt"></i>
                                                <span>Chèque</span>
                                            </span>
                                            <?php break; ?>
                                        <?php default: ?>
                                            <span class="status-badge">
                                                <i class="fas fa-question-circle"></i>
                                                <span><?php echo e($payment->payment_method); ?></span>
                                            </span>
                                    <?php endswitch; ?>
                                </td>
                                <td>
                                    <div class="d-flex flex-column">
                                        <span><?php echo e($payment->payment_date->format('d/m/Y')); ?></span>
                                        <span style="font-size: 0.75rem; color: var(--text-secondary);"><?php echo e($payment->payment_date->format('H:i')); ?></span>
                                    </div>
                                </td>
                                <td>
                                    <div class="action-buttons">
                                        <a href="<?php echo e(route('cashier.payments.show', $payment)); ?>" class="action-button view" title="Voir les détails">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if($payment->sale && ($payment->sale->payment_status ?? '') !== 'paid'): ?>
                                        <a href="<?php echo e(route('cashier.payments.process', $payment->sale)); ?>" class="action-button edit" title="Ajouter un paiement">
                                            <i class="fas fa-plus"></i>
                                        </a>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                            <tr>
                                <td colspan="5" class="text-center py-4">
                                    <div class="py-5">
                                        <i class="fas fa-receipt fa-3x text-muted mb-3"></i>
                                        <h4>Aucun paiement récent</h4>
                                        <p class="text-muted">Les paiements récents apparaîtront ici</p>
                                    </div>
                                </td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Section graphique pour visualiser les données -->
    <div class="dashboard-sections">
        <!-- Graphique des ventes et paiements -->
        <div class="chart-container fade-in fade-in-delay-3">
            <div class="chart-header">
                <h3 class="chart-title">Analyse des ventes et paiements</h3>
                <div class="chart-filters">
                    <button class="chart-filter active" data-period="week" onclick="updateChartPeriod('week')">Semaine</button>
                    <button class="chart-filter" data-period="month" onclick="updateChartPeriod('month')">Mois</button>
                    <button class="chart-filter" data-period="year" onclick="updateChartPeriod('year')">Année</button>
                </div>
            </div>
            <div class="chart-body">
                <canvas id="salesPaymentsChart" height="300"></canvas>
            </div>
        </div>
        
        <!-- Graphique de répartition des méthodes de paiement -->
        <div class="chart-container fade-in fade-in-delay-4">
            <div class="chart-header">
                <h3 class="chart-title">Méthodes de paiement</h3>
            </div>
            <div class="chart-body">
                <canvas id="paymentMethodsChart" height="300"></canvas>
            </div>
        </div>
    </div>
    
    <!-- Script pour initialiser les graphiques -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Données pour le graphique des ventes et paiements (7 derniers jours)
            const salesData = [
                <?php echo e($stats['daily_sales_amount'] ?? 0); ?>,
                <?php echo e(isset($stats['weekly_sales_amount']) ? ($stats['weekly_sales_amount'] / 7) : 0); ?>,
                <?php echo e(isset($stats['weekly_sales_amount']) ? ($stats['weekly_sales_amount'] / 7) : 0); ?>,
                <?php echo e(isset($stats['weekly_sales_amount']) ? ($stats['weekly_sales_amount'] / 7) : 0); ?>,
                <?php echo e(isset($stats['weekly_sales_amount']) ? ($stats['weekly_sales_amount'] / 7) : 0); ?>,
                <?php echo e(isset($stats['weekly_sales_amount']) ? ($stats['weekly_sales_amount'] / 7) : 0); ?>,
                <?php echo e(isset($stats['weekly_sales_amount']) ? ($stats['weekly_sales_amount'] / 7) : 0); ?>

            ];
            
            const paymentsData = [
                <?php echo e($stats['daily_payments_amount'] ?? 0); ?>,
                <?php echo e(isset($stats['weekly_payments_amount']) ? ($stats['weekly_payments_amount'] / 7) : 0); ?>,
                <?php echo e(isset($stats['weekly_payments_amount']) ? ($stats['weekly_payments_amount'] / 7) : 0); ?>,
                <?php echo e(isset($stats['weekly_payments_amount']) ? ($stats['weekly_payments_amount'] / 7) : 0); ?>,
                <?php echo e(isset($stats['weekly_payments_amount']) ? ($stats['weekly_payments_amount'] / 7) : 0); ?>,
                <?php echo e(isset($stats['weekly_payments_amount']) ? ($stats['weekly_payments_amount'] / 7) : 0); ?>,
                <?php echo e(isset($stats['weekly_payments_amount']) ? ($stats['weekly_payments_amount'] / 7) : 0); ?>

            ];
            
            // Générer les labels pour les 7 derniers jours
            const today = new Date();
            const labels = [];
            
            for (let i = 6; i >= 0; i--) {
                const date = new Date();
                date.setDate(today.getDate() - i);
                labels.push(date.toLocaleDateString('fr-FR', { weekday: 'short', day: 'numeric' }));
            }
            
            // Initialiser le graphique des ventes et paiements
            const salesPaymentsCtx = document.getElementById('salesPaymentsChart').getContext('2d');
            const salesPaymentsChart = new Chart(salesPaymentsCtx, {
                type: 'line',
                data: {
                    labels: labels,
                    datasets: [
                        {
                            label: 'Ventes',
                            data: salesData,
                            borderColor: '#1E88E5',
                            backgroundColor: 'rgba(30, 136, 229, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        },
                        {
                            label: 'Paiements',
                            data: paymentsData,
                            borderColor: '#4CAF50',
                            backgroundColor: 'rgba(76, 175, 80, 0.1)',
                            borderWidth: 2,
                            fill: true,
                            tension: 0.4
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    let label = context.dataset.label || '';
                                    if (label) {
                                        label += ': ';
                                    }
                                    if (context.parsed.y !== null) {
                                        label += new Intl.NumberFormat('fr-FR').format(context.parsed.y) + ' FCFA';
                                    }
                                    return label;
                                }
                            }
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString('fr-FR') + ' FCFA';
                                }
                            }
                        }
                    }
                }
            });
            
            // Données pour le graphique des méthodes de paiement
            const paymentMethodsCtx = document.getElementById('paymentMethodsChart').getContext('2d');
            const paymentMethodsChart = new Chart(paymentMethodsCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Espèces', 'Virement', 'Chèque', 'Autres'],
                    datasets: [{
                        data: [65, 20, 10, 5],
                        backgroundColor: [
                            '#4CAF50',
                            '#1E88E5',
                            '#FF9800',
                            '#9E9E9E'
                        ],
                        borderWidth: 1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'right',
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = Math.round((value / total) * 100);
                                    return `${label}: ${percentage}%`;
                                }
                            }
                        }
                    },
                }
            });
            
            // Fonction pour mettre à jour la période du graphique
            window.updateChartPeriod = function(period) {
                // Mettre à jour les filtres actifs
                document.querySelectorAll('.chart-filter').forEach(btn => {
                    btn.classList.remove('active');
                });
                document.querySelector(`.chart-filter[data-period="${period}"]`).classList.add('active');
                
                // Dans une application réelle, on ferait une requête AJAX pour obtenir les données
                // Pour cette démo, on simule des données différentes
                let newSalesData = [];
                let newPaymentsData = [];
                let newLabels = [];
                
                if (period === 'week') {
                    // Données pour la semaine (7 derniers jours)
                    const today = new Date();
                    for (let i = 6; i >= 0; i--) {
                        const date = new Date();
                        date.setDate(today.getDate() - i);
                        newLabels.push(date.toLocaleDateString('fr-FR', { weekday: 'short', day: 'numeric' }));
                    }
                    newSalesData = salesData;
                    newPaymentsData = paymentsData;
                } else if (period === 'month') {
                    // Données pour le mois (4 semaines)
                    for (let i = 0; i < 4; i++) {
                        newLabels.push(`Semaine ${i+1}`);
                    }
                    newSalesData = [<?php echo e($stats['weekly_sales_amount'] ?? 0); ?>, <?php echo e($stats['weekly_sales_amount'] * 0.9 ?? 0); ?>, <?php echo e($stats['weekly_sales_amount'] * 1.1 ?? 0); ?>, <?php echo e($stats['weekly_sales_amount'] * 0.95 ?? 0); ?>];
                    newPaymentsData = [<?php echo e($stats['weekly_payments_amount'] ?? 0); ?>, <?php echo e($stats['weekly_payments_amount'] * 0.85 ?? 0); ?>, <?php echo e($stats['weekly_payments_amount'] * 1.05 ?? 0); ?>, <?php echo e($stats['weekly_payments_amount'] * 0.9 ?? 0); ?>];
                } else if (period === 'year') {
                    // Données pour l'année (12 mois)
                    const months = ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Juin', 'Juil', 'Août', 'Sep', 'Oct', 'Nov', 'Déc'];
                    newLabels = months;
                    
                    // Simuler des données pour l'année
                    const monthlyBase = <?php echo e($stats['monthly_sales_amount'] ?? 1000000); ?>;
                    newSalesData = months.map((_, i) => monthlyBase * (0.8 + Math.random() * 0.4));
                    newPaymentsData = months.map((_, i) => monthlyBase * 0.9 * (0.8 + Math.random() * 0.4));
                }
                
                // Mettre à jour le graphique
                salesPaymentsChart.data.labels = newLabels;
                salesPaymentsChart.data.datasets[0].data = newSalesData;
                salesPaymentsChart.data.datasets[1].data = newPaymentsData;
                salesPaymentsChart.update();
            };
        });
    </script>


</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.cashier', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\xampp\htdocs\gradis\resources\views/cashier/dashboard.blade.php ENDPATH**/ ?>
<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\Sale;
use App\Models\Product;

class SaleController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:admin');
    }

    public function index()
    {
        $sales = Sale::with(['product.category', 'customer'])
            ->orderByDesc('created_at')
            ->paginate(15);

        // Récupérer les ventes avec remise en attente de validation
        $pendingDiscountSales = Sale::where('admin_validation_status', 'pending')
            ->whereNotNull('discount_per_ton')
            ->where('discount_per_ton', '>', 0)
            ->orderByDesc('created_at')
            ->get();

        // Récupérer les ventes avec augmentation de prix en attente de validation
        $pendingPriceIncreaseSales = Sale::where('admin_validation_status', 'pending')
            ->where('price_modified', true)
            ->orderByDesc('created_at')
            ->get();

        // Calculer les statistiques pour le dashboard
        $totalSales = Sale::count();
        $paidSales = Sale::where('payment_status', 'paid')->count();
        $pendingSales = Sale::where('payment_status', 'pending')->count();
        $totalRevenue = Sale::sum(\DB::raw('quantity * unit_price - COALESCE(discount_total, 0)'));

        // Statistiques pour la période actuelle (ce mois)
        $currentMonth = now()->startOfMonth();
        $monthlySales = Sale::where('created_at', '>=', $currentMonth)->count();
        $monthlyRevenue = Sale::where('created_at', '>=', $currentMonth)
            ->sum(\DB::raw('quantity * unit_price - COALESCE(discount_total, 0)'));

        return view('admin.sales.index', compact(
            'sales',
            'pendingDiscountSales',
            'pendingPriceIncreaseSales',
            'totalSales',
            'paidSales',
            'pendingSales',
            'totalRevenue',
            'monthlySales',
            'monthlyRevenue'
        ));
    }

    public function create()
    {
        $products = Product::where('is_active', true)->get();
        return view('admin.sales.create', compact('products'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'unit_price' => 'required|numeric|min:0',
            'total_price' => 'required|numeric|min:0',
            'payment_status' => 'required|in:pending,paid,cancelled',
            'payment_method' => 'required|in:cash,card,transfer',
            'notes' => 'nullable|string|max:1000',
        ]);

        $sale = Sale::create($validated + ['user_id' => auth()->id()]);

        return redirect()->route('admin.sales.index')
            ->with('success', 'Vente créée avec succès.');
    }

    public function show(Sale $sale)
    {
        $sale->load(['product', 'supply', 'city', 'createdBy']);
        return view('admin.sales.show', compact('sale'));
    }

    /**
     * Annuler une vente et remettre la quantité sur le camion
     */
    public function cancel(Sale $sale)
    {
        try {
            DB::beginTransaction();

            // Vérifier que la vente n'est pas déjà annulée
            if ($sale->status === 'cancelled') {
                return response()->json([
                    'success' => false,
                    'message' => 'Cette vente est déjà annulée.'
                ], 422);
            }

            // Charger les relations nécessaires
            $sale->load(['supply', 'product']);

            // Récupérer les informations de la vente
            $supply = $sale->supply;
            $product = $sale->product;
            $cityId = $sale->city_id;
            $quantity = $sale->quantity;

            if (!$supply || !$product) {
                return response()->json([
                    'success' => false,
                    'message' => 'Impossible de retrouver les informations de la vente (approvisionnement ou produit manquant).'
                ], 422);
            }

            // Trouver l'enregistrement SupplyCity correspondant à cette vente
            // On utilise les informations de la vente pour retrouver le bon enregistrement
            $supplyCity = \App\Models\SupplyCity::where('supply_id', $supply->id)
                ->where('city_id', $cityId)
                ->with('vehicle') // Charger la relation vehicle pour afficher les infos
                ->first();

            if (!$supplyCity) {
                return response()->json([
                    'success' => false,
                    'message' => 'Impossible de retrouver les informations du camion pour cette vente.'
                ], 422);
            }

            // Remettre la quantité sur le camion
            $supplyCity->remaining_quantity += $quantity;
            $supplyCity->save();

            // Remettre la quantité dans le stock général du produit
            $product->stock_quantity += $quantity;
            $product->save();

            // Marquer la vente comme annulée
            $sale->status = 'cancelled';
            $sale->payment_status = 'cancelled';
            $sale->admin_note = 'Vente annulée par l\'administrateur le ' . now()->format('d/m/Y à H:i');
            $sale->save();

            DB::commit();

            $vehicleInfo = $supplyCity->vehicle ? $supplyCity->vehicle->registration_number : 'N/A';

            return response()->json([
                'success' => true,
                'message' => sprintf(
                    'Vente N°%s annulée avec succès. La quantité de %s tonnes a été remise sur le camion %s.',
                    $sale->id,
                    number_format($quantity, 2),
                    $vehicleInfo
                )
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return response()->json([
                'success' => false,
                'message' => 'Erreur lors de l\'annulation de la vente : ' . $e->getMessage()
            ], 500);
        }
    }

    public function edit(Sale $sale)
    {
        $products = Product::where('is_active', true)->get();
        return view('admin.sales.edit', compact('sale', 'products'));
    }

    public function update(Request $request, Sale $sale)
    {
        $validated = $request->validate([
            'product_id' => 'required|exists:products,id',
            'quantity' => 'required|integer|min:1',
            'unit_price' => 'required|numeric|min:0',
            'total_price' => 'required|numeric|min:0',
            'payment_status' => 'required|in:pending,paid,cancelled',
            'payment_method' => 'required|in:cash,card,transfer',
            'notes' => 'nullable|string|max:1000',
        ]);

        $sale->update($validated);

        return redirect()->route('admin.sales.index')
            ->with('success', 'Vente mise à jour avec succès.');
    }

    public function destroy(Sale $sale)
    {
        // Réajouter la quantité vendue au stock du produit
        if ($sale->product) {
            $sale->product->increment('stock_quantity', $sale->quantity);
        }
        $sale->delete();

        return redirect()->route('admin.sales.index')
            ->with('success', 'Vente supprimée avec succès. La quantité a été réajoutée au stock.');
    }

    public function updateSaleDate(Request $request, Sale $sale)
    {
        $request->validate([
            'sale_date' => 'required|date',
            'sale_time' => 'required|date_format:H:i',
        ]);

        try {
            // Combiner la date et l'heure
            $newDateTime = $request->sale_date . ' ' . $request->sale_time . ':00';

            // Mettre à jour la date de création de la vente
            $sale->created_at = $newDateTime;
            $sale->updated_at = now(); // Garder la date de modification actuelle
            $sale->save();

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'Date de vente modifiée avec succès.'
                ]);
            }

            return redirect()->route('admin.sales.index')
                ->with('success', 'Date de vente modifiée avec succès.');

        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Erreur lors de la modification de la date: ' . $e->getMessage()
                ], 500);
            }

            return redirect()->back()
                ->with('error', 'Erreur lors de la modification de la date: ' . $e->getMessage());
        }
    }

    public function validateDiscountSale(Sale $sale, Request $request)
    {
        $request->validate([
            'action' => 'required|in:approve,reject',
            'admin_note' => 'nullable|string|max:1000',
        ]);

        // Déterminer le type de vente (remise ou augmentation de prix)
        $saleType = $sale->price_modified ? 'price_increase' : 'discount';

        if ($request->action === 'approve') {
            $sale->admin_validation_status = 'approved';
            
            // Message de succès en fonction du type de vente
            if ($saleType === 'price_increase') {
                $successMessage = 'Vente avec augmentation de prix approuvée avec succès.';
            } else {
                $successMessage = 'Vente avec remise approuvée avec succès.';
            }
        } else {
            // Cas de rejet
            $sale->admin_validation_status = 'rejected';
            
            // Enregistrer la raison du rejet
            $sale->admin_note = $request->admin_note;
            
            // Récupérer le produit associé à la vente pour mettre à jour le stock
            if ($sale->product) {
                // Retourner la quantité au stock du produit
                $sale->product->increment('stock_quantity', $sale->quantity);
            }
            
            // Retourner la quantité à la carte de la vente (supply_city)
            if ($sale->supply_id) {
                // Récupérer la carte de la vente (supply_city) correspondante
                $supplyCity = \App\Models\SupplyCity::where('supply_id', $sale->supply_id)
                    ->where('city_id', $sale->city_id)
                    ->where('vehicle_id', $sale->vehicle_id)
                    ->first();
                
                if ($supplyCity) {
                    // Si remaining_quantity est null, l'initialiser avec la quantité initiale
                    if ($supplyCity->remaining_quantity === null) {
                        $supplyCity->remaining_quantity = $supplyCity->quantity;
                    }
                    
                    // Ajouter la quantité rejetée à la quantité restante
                    $supplyCity->remaining_quantity += $sale->quantity;
                    $supplyCity->save();
                    
                    // Journaliser l'action pour le débogage
                    \Illuminate\Support\Facades\Log::info('Vente #' . $sale->id . ' rejetée: Quantité ' . $sale->quantity . ' tonnes retournée au card #' . $supplyCity->id . ' (Véhicule: ' . $sale->vehicle_id . ')');
                } else {
                    // Si on ne trouve pas la carte correspondante, journaliser l'erreur
                    \Illuminate\Support\Facades\Log::warning('Vente #' . $sale->id . ' rejetée: Impossible de trouver la carte correspondante pour retourner la quantité');
                }
            }
            
            // Message de succès en fonction du type de vente
            if ($saleType === 'price_increase') {
                $successMessage = 'Vente avec augmentation de prix rejetée. La quantité a été retournée au stock et à la carte correspondante.';
            } else {
                $successMessage = 'Vente avec remise rejetée. La quantité a été retournée au stock et à la carte correspondante.';
            }
        }
        
        $sale->save();

        return redirect()->route('admin.sales.index')
            ->with('success', $successMessage);
    }

    /**
     * Exporter toutes les ventes
     */
    public function export()
    {
        $sales = Sale::with(['product', 'customer'])->get();

        $filename = 'ventes_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($sales) {
            $file = fopen('php://output', 'w');

            // En-têtes CSV
            fputcsv($file, [
                'ID',
                'Date',
                'Produit',
                'Client',
                'Quantité',
                'Prix unitaire',
                'Prix total',
                'Remise',
                'Montant final',
                'Statut'
            ]);

            // Données
            foreach ($sales as $sale) {
                fputcsv($file, [
                    $sale->id,
                    $sale->created_at->format('d/m/Y H:i'),
                    $sale->product->name ?? 'N/A',
                    $sale->customer_name ?? 'N/A',
                    $sale->quantity,
                    $sale->unit_price,
                    $sale->quantity * $sale->unit_price,
                    $sale->discount_total ?? 0,
                    ($sale->quantity * $sale->unit_price) - ($sale->discount_total ?? 0),
                    $sale->payment_status
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Exporter les ventes sélectionnées
     */
    public function exportSelected(Request $request)
    {
        $saleIds = $request->input('sale_ids', []);
        $sales = Sale::with(['product', 'customer'])->whereIn('id', $saleIds)->get();

        $filename = 'ventes_selection_' . now()->format('Y-m-d_H-i-s') . '.csv';

        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($sales) {
            $file = fopen('php://output', 'w');

            // En-têtes CSV
            fputcsv($file, [
                'ID',
                'Date',
                'Produit',
                'Client',
                'Quantité',
                'Prix unitaire',
                'Prix total',
                'Remise',
                'Montant final',
                'Statut'
            ]);

            // Données
            foreach ($sales as $sale) {
                fputcsv($file, [
                    $sale->id,
                    $sale->created_at->format('d/m/Y H:i'),
                    $sale->product->name ?? 'N/A',
                    $sale->customer_name ?? 'N/A',
                    $sale->quantity,
                    $sale->unit_price,
                    $sale->quantity * $sale->unit_price,
                    $sale->discount_total ?? 0,
                    ($sale->quantity * $sale->unit_price) - ($sale->discount_total ?? 0),
                    $sale->payment_status
                ]);
            }

            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Marquer les ventes sélectionnées comme payées
     */
    public function bulkMarkPaid(Request $request)
    {
        $saleIds = $request->input('sale_ids', []);

        $updated = Sale::whereIn('id', $saleIds)
            ->where('payment_status', '!=', 'paid')
            ->update(['payment_status' => 'paid']);

        return redirect()->route('admin.sales.index')
            ->with('success', "{$updated} vente(s) marquée(s) comme payée(s).");
    }

    /**
     * Supprimer les ventes sélectionnées
     */
    public function deleteSelected(Request $request)
    {
        $saleIds = $request->input('sale_ids', []);

        $deleted = Sale::whereIn('id', $saleIds)->delete();

        return redirect()->route('admin.sales.index')
            ->with('success', "{$deleted} vente(s) supprimée(s).");
    }

    /**
     * Générer une facture pour une vente
     */
    public function generateInvoice(Sale $sale)
    {
        return view('admin.sales.invoice', compact('sale'));
    }
}
